<?php
/**
 * Simple test API endpoint to verify database connection
 * Upload this file to: https://www.intelligentmyanmar.com/OrderCollectionApp(DemoROasis)/api/test.php
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Database configuration
$host = '*************';
$dbname = 'DemoROasis';
$username = 'DemoROasisUser';
$password = '!nt3ll!g3nt';

try {
    // Create PDO connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test query - get first 5 products
    $sql = "
        SELECT 
            p.ProductID,
            p.`Product Code`,
            p.ProductName,
            p.`Product Description`,
            p.CategoryID,
            c.CategoryName,
            p.PrincipalID,
            pr.PrincipalName,
            p.`Unit Size`,
            p.`Selling Price`,
            p.ProductImagePath,
            p.UnitsInStock,
            p.Discontinued
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Principals pr ON p.PrincipalID = pr.PrincipalID
        WHERE p.Discontinued = 0
        ORDER BY p.ProductName ASC 
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM Products WHERE Discontinued = 0";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Database connection successful',
        'data' => $products,
        'total_products' => (int)$total,
        'sample_count' => count($products),
        'server_time' => date('Y-m-d H:i:s'),
        'database_info' => [
            'host' => $host,
            'database' => $dbname,
            'connection_status' => 'Connected'
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // Handle database errors
    $response = [
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage(),
        'data' => null,
        'error_code' => $e->getCode(),
        'server_time' => date('Y-m-d H:i:s')
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Handle other errors
    $response = [
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'data' => null,
        'server_time' => date('Y-m-d H:i:s')
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
}
?>
