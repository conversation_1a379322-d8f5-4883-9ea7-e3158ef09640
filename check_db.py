#!/usr/bin/env python3
import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>

def connect_and_check_db():
    try:
        # Database connection parameters
        connection = mysql.connector.connect(
            host='*************',
            database='DemoROasis',
            user='DemoROasisUser',
            password='!nt3ll!g3nt',
            port=3306
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            print("=== DATABASE CONNECTION SUCCESSFUL ===")
            print(f"Connected to MySQL Server version: {connection.get_server_info()}")
            print(f"Database: DemoROasis")
            print()
            
            # Show all tables
            print("=== ALL TABLES IN DATABASE ===")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            for table in tables:
                print(f"- {table[0]}")
            print()
            
            # Check specific tables
            target_tables = ['Products', 'Customers', 'OrderRequest', 'OrderRequestDetails', 'PriceList']
            
            for table_name in target_tables:
                print(f"=== TABLE: {table_name} ===")
                try:
                    # Check if table exists
                    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if cursor.fetchone():
                        # Show table structure
                        cursor.execute(f"DESCRIBE {table_name}")
                        columns = cursor.fetchall()
                        
                        print(f"Structure of {table_name}:")
                        print("Column Name | Data Type | Null | Key | Default | Extra")
                        print("-" * 60)
                        for column in columns:
                            print(f"{column[0]:<15} | {column[1]:<15} | {column[2]:<4} | {column[3]:<3} | {str(column[4]):<7} | {column[5]}")
                        
                        # Show row count
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        print(f"\nTotal rows: {count}")
                        
                        # Show sample data (first 3 rows)
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                        sample_data = cursor.fetchall()
                        if sample_data:
                            print("\nSample data (first 3 rows):")
                            for row in sample_data:
                                print(row)
                        
                    else:
                        print(f"Table '{table_name}' does not exist!")
                        
                except Error as e:
                    print(f"Error checking table {table_name}: {e}")
                
                print("\n" + "="*80 + "\n")
            
    except Error as e:
        print(f"Error connecting to MySQL: {e}")
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL connection closed.")

if __name__ == "__main__":
    connect_and_check_db()
