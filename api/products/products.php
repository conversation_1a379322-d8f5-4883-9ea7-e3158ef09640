<?php
/**
 * Products API endpoint
 * Upload to: /api/products/products.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get request parameters
    $search = $_GET['search'] ?? null;
    $categoryId = $_GET['category_id'] ?? null;
    $principalId = $_GET['principal_id'] ?? null;
    $productId = $_GET['product_id'] ?? null;
    $productCode = $_GET['product_code'] ?? null;
    $activeOnly = $_GET['active_only'] ?? true;
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 50);

    // If limit is very high (like 10000), get all products
    if ($limit >= 1000) {
        $limit = 999999; // Effectively no limit
        $offset = 0;
        $page = 1;
    } else {
        $offset = ($page - 1) * $limit;
    }
    
    // Build the SQL query with PriceList integration
    $sql = "
        SELECT
            p.ProductID,
            p.`Product Code`,
            p.ProductName,
            p.`Product Description`,
            p.CategoryID,
            c.CategoryName,
            p.PrincipalID,
            pr.PrincipalName,
            p.`Unit Size`,
            p.`Selling Price`,
            COALESCE(pl.Price, p.`Selling Price`) as CurrentPrice,
            pl.PriceDescription,
            p.ProductImagePath,
            p.UnitsInStock,
            p.Discontinued
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Principals pr ON p.PrincipalID = pr.PrincipalID
        LEFT JOIN PriceList pl ON p.ProductID = pl.ProductID
            AND pl.PriceDescriptionID = 1
            AND pl.ActivePrice = 1
        WHERE 1=1
    ";
    
    $params = [];
    
    // Add specific product ID filter
    if ($productId) {
        $sql .= " AND p.ProductID = :productId";
        $params['productId'] = $productId;
    }
    
    // Add specific product code filter
    if ($productCode) {
        $sql .= " AND p.`Product Code` = :productCode";
        $params['productCode'] = $productCode;
    }
    
    // Add search filter
    if ($search) {
        $sql .= " AND (p.ProductName LIKE :search OR p.`Product Code` LIKE :search OR p.`Product Description` LIKE :search)";
        $params['search'] = "%$search%";
    }
    
    // Add category filter
    if ($categoryId) {
        $sql .= " AND p.CategoryID = :categoryId";
        $params['categoryId'] = $categoryId;
    }
    
    // Add principal filter
    if ($principalId) {
        $sql .= " AND p.PrincipalID = :principalId";
        $params['principalId'] = $principalId;
    }
    
    // Add active only filter
    if ($activeOnly && $activeOnly !== 'false') {
        $sql .= " AND p.Discontinued = 0";
    }
    
    // Add ordering and pagination
    $sql .= " ORDER BY p.ProductName ASC LIMIT :limit OFFSET :offset";
    
    // Prepare and execute the query
    $stmt = $pdo->prepare($sql);
    
    // Bind parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $rawProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format products to ensure consistent data types
    $products = [];
    foreach ($rawProducts as $product) {
        $products[] = [
            'ProductID' => (int)$product['ProductID'],
            'Product Code' => (string)$product['Product Code'],
            'ProductName' => (string)$product['ProductName'],
            'Product Description' => (string)($product['Product Description'] ?? ''),
            'CategoryID' => $product['CategoryID'] ? (int)$product['CategoryID'] : null,
            'CategoryName' => (string)($product['CategoryName'] ?? ''),
            'PrincipalID' => $product['PrincipalID'] ? (int)$product['PrincipalID'] : null,
            'PrincipalName' => (string)($product['PrincipalName'] ?? ''),
            'Unit Size' => (string)($product['Unit Size'] ?? ''),
            'Selling Price' => (float)$product['Selling Price'],
            'Current Price' => (float)$product['CurrentPrice'],
            'Price Description' => (string)($product['PriceDescription'] ?? ''),
            'ProductImagePath' => (string)($product['ProductImagePath'] ?? ''),
            'UnitsInStock' => (float)$product['UnitsInStock'],
            'Discontinued' => (int)$product['Discontinued']
        ];
    }
    
    // Get total count for pagination
    $countSql = "
        SELECT COUNT(*) as total
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Principals pr ON p.PrincipalID = pr.PrincipalID
        WHERE 1=1
    ";
    
    $countParams = [];
    if ($productId) {
        $countSql .= " AND p.ProductID = :productId";
        $countParams['productId'] = $productId;
    }
    if ($productCode) {
        $countSql .= " AND p.`Product Code` = :productCode";
        $countParams['productCode'] = $productCode;
    }
    if ($search) {
        $countSql .= " AND (p.ProductName LIKE :search OR p.`Product Code` LIKE :search OR p.`Product Description` LIKE :search)";
        $countParams['search'] = "%$search%";
    }
    if ($categoryId) {
        $countSql .= " AND p.CategoryID = :categoryId";
        $countParams['categoryId'] = $categoryId;
    }
    if ($principalId) {
        $countSql .= " AND p.PrincipalID = :principalId";
        $countParams['principalId'] = $principalId;
    }
    if ($activeOnly && $activeOnly !== 'false') {
        $countSql .= " AND p.Discontinued = 0";
    }
    
    $countStmt = $pdo->prepare($countSql);
    foreach ($countParams as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Products retrieved successfully',
        'data' => $products,
        'returned_count' => count($products),
        'total' => (int)$total,
        'page' => $page,
        'limit' => $limit == 999999 ? 'ALL' : $limit,
        'total_pages' => $limit == 999999 ? 1 : ceil($total / $limit)
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // Handle database errors
    $response = [
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Handle other errors
    $response = [
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
}
?>
