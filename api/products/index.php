<?php
/**
 * Products API - Main endpoint
 * Upload to: /api/products/index.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get request parameters
    $search = $_GET['search'] ?? null;
    $categoryId = $_GET['category_id'] ?? null;
    $principalId = $_GET['principal_id'] ?? null;
    $productId = $_GET['product_id'] ?? null;
    $productCode = $_GET['product_code'] ?? null;
    $activeOnly = $_GET['active_only'] ?? true;
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 50);
    $offset = ($page - 1) * $limit;
    
    // Build the SQL query
    $sql = "
        SELECT 
            p.ProductID,
            p.`Product Code`,
            p.ProductName,
            p.`Product Description`,
            p.CategoryID,
            c.CategoryName,
            p.PrincipalID,
            pr.PrincipalName,
            p.`Unit Size`,
            p.`Selling Price`,
            p.ProductImagePath,
            p.UnitsInStock,
            p.Discontinued
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Principals pr ON p.PrincipalID = pr.PrincipalID
        WHERE 1=1
    ";
    
    $params = [];
    
    // Add filters
    if ($productId) {
        $sql .= " AND p.ProductID = :productId";
        $params['productId'] = $productId;
    }
    
    if ($productCode) {
        $sql .= " AND p.`Product Code` = :productCode";
        $params['productCode'] = $productCode;
    }
    
    if ($search) {
        $sql .= " AND (p.ProductName LIKE :search OR p.`Product Code` LIKE :search OR p.`Product Description` LIKE :search)";
        $params['search'] = "%$search%";
    }
    
    if ($categoryId) {
        $sql .= " AND p.CategoryID = :categoryId";
        $params['categoryId'] = $categoryId;
    }
    
    if ($principalId) {
        $sql .= " AND p.PrincipalID = :principalId";
        $params['principalId'] = $principalId;
    }
    
    if ($activeOnly && $activeOnly !== 'false') {
        $sql .= " AND p.Discontinued = 0";
    }
    
    // Add ordering and pagination
    $sql .= " ORDER BY p.ProductName ASC LIMIT :limit OFFSET :offset";
    
    // Execute query
    $stmt = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $countSql = str_replace("SELECT p.ProductID,", "SELECT COUNT(*) as total FROM (SELECT p.ProductID", $sql);
    $countSql = str_replace("ORDER BY p.ProductName ASC LIMIT :limit OFFSET :offset", ") as subquery", $countSql);
    
    $countStmt = $pdo->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Return response
    echo json_encode([
        'success' => true,
        'message' => 'Products retrieved successfully',
        'data' => $products,
        'total' => (int)$total,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => ceil($total / $limit)
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
