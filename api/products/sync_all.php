<?php
/**
 * Sync All Products API endpoint - Gets ALL products without pagination
 * Upload to: /api/products/sync_all.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get ALL active products without pagination with current prices
    $sql = "
        SELECT
            p.ProductID,
            p.`Product Code`,
            p.ProductName,
            p.`Product Description`,
            p.CategoryID,
            c.CategoryName,
            p.PrincipalID,
            pr.PrincipalName,
            p.`Unit Size`,
            p.`Selling Price`,
            COALESCE(pl.Price, p.`Selling Price`) as CurrentPrice,
            pl.PriceDescription,
            p.ProductImagePath,
            p.UnitsInStock,
            p.Discontinued
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Principals pr ON p.PrincipalID = pr.PrincipalID
        LEFT JOIN PriceList pl ON p.ProductID = pl.ProductID
            AND pl.PriceDescriptionID = 1
            AND pl.ActivePrice = 1
        WHERE p.Discontinued = 0
        ORDER BY p.ProductName ASC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $rawProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format products to ensure consistent data types
    $products = [];
    foreach ($rawProducts as $product) {
        $products[] = [
            'ProductID' => (int)$product['ProductID'],
            'Product Code' => (string)$product['Product Code'],
            'ProductName' => (string)$product['ProductName'],
            'Product Description' => (string)($product['Product Description'] ?? ''),
            'CategoryID' => $product['CategoryID'] ? (int)$product['CategoryID'] : null,
            'CategoryName' => (string)($product['CategoryName'] ?? ''),
            'PrincipalID' => $product['PrincipalID'] ? (int)$product['PrincipalID'] : null,
            'PrincipalName' => (string)($product['PrincipalName'] ?? ''),
            'Unit Size' => (string)($product['Unit Size'] ?? ''),
            'Selling Price' => (float)$product['Selling Price'],
            'Current Price' => (float)$product['CurrentPrice'],
            'Price Description' => (string)($product['PriceDescription'] ?? ''),
            'ProductImagePath' => (string)($product['ProductImagePath'] ?? ''),
            'UnitsInStock' => (float)$product['UnitsInStock'],
            'Discontinued' => (int)$product['Discontinued']
        ];
    }
    
    // Get total count
    $countStmt = $pdo->query("SELECT COUNT(*) as total FROM Products WHERE Discontinued = 0");
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'All products retrieved successfully',
        'data' => $products,
        'returned_count' => count($products),
        'total' => (int)$total,
        'sync_type' => 'ALL_PRODUCTS',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // Handle database errors
    $response = [
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Handle other errors
    $response = [
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
}
?>
