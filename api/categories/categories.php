<?php
/**
 * Categories API endpoint
 * Upload to: /api/categories/categories.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get all categories
    $sql = "
        SELECT 
            c.CategoryID,
            c.CategoryName,
            c.CategoryDescription,
            COUNT(p.ProductID) as ProductCount
        FROM Categories c
        LEFT JOIN Products p ON c.CategoryID = p.CategoryID AND p.Discontinued = 0
        GROUP BY c.CategoryID, c.CategoryName, c.CategoryDescription
        ORDER BY c.CategoryName ASC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Categories retrieved successfully',
        'data' => $categories,
        'total' => count($categories)
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
