<?php
/**
 * SaleTeams API endpoint
 * Upload to: /api/saleteams/saleteams.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get all sale teams
    $sql = "
        SELECT
            st.SaleTeamID,
            st.SaleTeam,
            st.SaleTeamDescription,
            COUNT(c.CustomerID) as CustomerCount
        FROM SaleTeam st
        LEFT JOIN Customers c ON st.SaleTeamID = c.SaleTeamID AND c.Active = 1
        GROUP BY st.SaleTeamID, st.SaleTeam, st.SaleTeamDescription
        ORDER BY st.SaleTeam ASC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $rawSaleTeams = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format sale teams to ensure consistent data types
    $saleTeams = [];
    foreach ($rawSaleTeams as $saleTeam) {
        $saleTeams[] = [
            'SaleTeamID' => (int)$saleTeam['SaleTeamID'],
            'SaleTeam' => (string)$saleTeam['SaleTeam'],
            'SaleTeamDescription' => (string)($saleTeam['SaleTeamDescription'] ?? ''),
            'CustomerCount' => (int)$saleTeam['CustomerCount']
        ];
    }
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Sale teams retrieved successfully',
        'data' => $saleTeams,
        'total' => count($saleTeams)
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
