<?php
/**
 * Dynamic SaleTeams API endpoint - adapts to actual table structure
 * Upload to: /api/saleteams/saleteams_dynamic.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // First, check what columns exist in the SaleTeam table
    $stmt = $pdo->query("DESCRIBE SaleTeam");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $columnNames = array_column($columns, 'Field');
    
    // Determine the correct field names
    $idField = 'SaleTeamID'; // This should be standard
    $nameField = null;
    $descField = null;
    
    // Check for name field variations
    if (in_array('SaleTeam', $columnNames)) {
        $nameField = 'SaleTeam';
    } elseif (in_array('SaleTeamName', $columnNames)) {
        $nameField = 'SaleTeamName';
    } elseif (in_array('Name', $columnNames)) {
        $nameField = 'Name';
    }
    
    // Check for description field variations
    if (in_array('SaleTeamDescription', $columnNames)) {
        $descField = 'SaleTeamDescription';
    } elseif (in_array('Description', $columnNames)) {
        $descField = 'Description';
    }
    
    if (!$nameField) {
        throw new Exception("Could not find name field in SaleTeam table. Available columns: " . implode(', ', $columnNames));
    }
    
    // Build dynamic SQL query
    $sql = "
        SELECT 
            st.$idField,
            st.$nameField" . ($descField ? ",\n            st.$descField" : "") . ",
            COUNT(c.CustomerID) as CustomerCount
        FROM SaleTeam st
        LEFT JOIN Customers c ON st.$idField = c.SaleTeamID AND c.Active = 1
        GROUP BY st.$idField, st.$nameField" . ($descField ? ", st.$descField" : "") . "
        ORDER BY st.$nameField ASC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $rawSaleTeams = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format sale teams with standardized field names
    $saleTeams = [];
    foreach ($rawSaleTeams as $saleTeam) {
        $formatted = [
            'SaleTeamID' => (int)$saleTeam[$idField],
            'SaleTeam' => (string)$saleTeam[$nameField],
            'CustomerCount' => (int)$saleTeam['CustomerCount']
        ];
        
        if ($descField && isset($saleTeam[$descField])) {
            $formatted['SaleTeamDescription'] = (string)($saleTeam[$descField] ?? '');
        }
        
        $saleTeams[] = $formatted;
    }
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Sale teams retrieved successfully',
        'data' => $saleTeams,
        'total' => count($saleTeams),
        'table_info' => [
            'columns_found' => $columnNames,
            'id_field' => $idField,
            'name_field' => $nameField,
            'desc_field' => $descField
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
