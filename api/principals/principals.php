<?php
/**
 * Principals API endpoint
 * Upload to: /api/principals/principals.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get all principals
    $sql = "
        SELECT 
            pr.PrincipalID,
            pr.PrincipalName,
            pr.PrincipalDescription,
            COUNT(p.ProductID) as ProductCount
        FROM Principals pr
        LEFT JOIN Products p ON pr.PrincipalID = p.PrincipalID AND p.Discontinued = 0
        GROUP BY pr.PrincipalID, pr.PrincipalName, pr.PrincipalDescription
        ORDER BY pr.PrincipalName ASC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $principals = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Principals retrieved successfully',
        'data' => $principals,
        'total' => count($principals)
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
