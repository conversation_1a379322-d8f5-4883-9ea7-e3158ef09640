<?php
/**
 * Dynamic Customers API endpoint - adapts to actual table structure
 * Upload to: /api/customers/customers_dynamic.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Check table structures
    $customerColumns = [];
    $townshipColumns = [];
    $saleTeamColumns = [];
    
    // Get Customers table structure
    $stmt = $pdo->query("DESCRIBE Customers");
    $customerColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    
    // Get Townships table structure
    try {
        $stmt = $pdo->query("DESCRIBE Townships");
        $townshipColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    } catch (Exception $e) {
        // Townships table might not exist
    }
    
    // Get SaleTeam table structure
    try {
        $stmt = $pdo->query("DESCRIBE SaleTeam");
        $saleTeamColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    } catch (Exception $e) {
        // SaleTeam table might not exist
    }
    
    // Determine field names dynamically
    $townshipNameField = null;
    if (in_array('TownshipName', $townshipColumns)) {
        $townshipNameField = 'TownshipName';
    } elseif (in_array('Township', $townshipColumns)) {
        $townshipNameField = 'Township';
    }
    
    $saleTeamNameField = null;
    if (in_array('SaleTeam', $saleTeamColumns)) {
        $saleTeamNameField = 'SaleTeam';
    } elseif (in_array('SaleTeamName', $saleTeamColumns)) {
        $saleTeamNameField = 'SaleTeamName';
    }
    
    // Get request parameters
    $search = $_GET['search'] ?? null;
    $customerId = $_GET['customer_id'] ?? null;
    $customerCode = $_GET['customer_code'] ?? null;
    $townshipId = $_GET['township_id'] ?? null;
    $saleTeamId = $_GET['sale_team_id'] ?? null;
    $activeOnly = $_GET['active_only'] ?? true;
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 50);
    
    if ($limit >= 1000) {
        $limit = 999999;
        $offset = 0;
        $page = 1;
    } else {
        $offset = ($page - 1) * $limit;
    }
    
    // Build dynamic SQL query
    $sql = "
        SELECT 
            c.CustomerID,
            c.CustomerCode,
            c.CompanyName,
            c.ContactPerson1,
            c.ContactPerson2,
            c.Address,
            c.Phone,
            c.MobileNo,
            c.EmailAddress,
            c.CreditLimit,
            c.Active,
            c.TownshipID,
            c.SaleTeamID,
            c.Latitude,
            c.Longitude,
            c.CreationDate,
            c.ModifiedDate";
    
    // Add township name if available
    if ($townshipNameField && !empty($townshipColumns)) {
        $sql .= ",\n            t.$townshipNameField as TownshipName";
    }
    
    // Add sale team name if available
    if ($saleTeamNameField && !empty($saleTeamColumns)) {
        $sql .= ",\n            st.$saleTeamNameField as SaleTeam";
    }
    
    $sql .= "\n        FROM Customers c";
    
    // Add JOINs if tables exist
    if (!empty($townshipColumns)) {
        $sql .= "\n        LEFT JOIN Townships t ON c.TownshipID = t.TownshipID";
    }
    if (!empty($saleTeamColumns)) {
        $sql .= "\n        LEFT JOIN SaleTeam st ON c.SaleTeamID = st.SaleTeamID";
    }
    
    $sql .= "\n        WHERE 1=1";
    
    $params = [];
    
    // Add filters
    if ($customerId) {
        $sql .= " AND c.CustomerID = :customerId";
        $params['customerId'] = $customerId;
    }
    
    if ($customerCode) {
        $sql .= " AND c.CustomerCode = :customerCode";
        $params['customerCode'] = $customerCode;
    }
    
    if ($search) {
        $sql .= " AND (c.CompanyName LIKE :search OR c.CustomerCode LIKE :search OR c.ContactPerson1 LIKE :search OR c.EmailAddress LIKE :search)";
        $params['search'] = "%$search%";
    }
    
    if ($townshipId) {
        $sql .= " AND c.TownshipID = :townshipId";
        $params['townshipId'] = $townshipId;
    }
    
    if ($saleTeamId) {
        $sql .= " AND c.SaleTeamID = :saleTeamId";
        $params['saleTeamId'] = $saleTeamId;
    }
    
    if ($activeOnly && $activeOnly !== 'false') {
        $sql .= " AND c.Active = 1";
    }
    
    $sql .= " ORDER BY c.CompanyName ASC LIMIT :limit OFFSET :offset";
    
    // Execute query
    $stmt = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $rawCustomers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format customers
    $customers = [];
    foreach ($rawCustomers as $customer) {
        $formatted = [
            'CustomerID' => (int)$customer['CustomerID'],
            'CustomerCode' => (string)$customer['CustomerCode'],
            'CompanyName' => (string)$customer['CompanyName'],
            'ContactPerson1' => (string)($customer['ContactPerson1'] ?? ''),
            'ContactPerson2' => (string)($customer['ContactPerson2'] ?? ''),
            'Address' => (string)($customer['Address'] ?? ''),
            'Phone' => (string)($customer['Phone'] ?? ''),
            'MobileNo' => (string)($customer['MobileNo'] ?? ''),
            'EmailAddress' => (string)($customer['EmailAddress'] ?? ''),
            'CreditLimit' => (float)($customer['CreditLimit'] ?? 0),
            'Active' => (int)$customer['Active'],
            'TownshipID' => $customer['TownshipID'] ? (int)$customer['TownshipID'] : null,
            'TownshipName' => (string)($customer['TownshipName'] ?? ''),
            'SaleTeamID' => $customer['SaleTeamID'] ? (int)$customer['SaleTeamID'] : null,
            'SaleTeam' => (string)($customer['SaleTeam'] ?? ''),
            'Latitude' => $customer['Latitude'] ? (float)$customer['Latitude'] : null,
            'Longitude' => $customer['Longitude'] ? (float)$customer['Longitude'] : null,
            'CreationDate' => (string)($customer['CreationDate'] ?? ''),
            'ModifiedDate' => (string)($customer['ModifiedDate'] ?? '')
        ];
        
        $customers[] = $formatted;
    }
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM Customers c WHERE 1=1";
    $countParams = [];
    
    if ($customerId) {
        $countSql .= " AND c.CustomerID = :customerId";
        $countParams['customerId'] = $customerId;
    }
    if ($customerCode) {
        $countSql .= " AND c.CustomerCode = :customerCode";
        $countParams['customerCode'] = $customerCode;
    }
    if ($search) {
        $countSql .= " AND (c.CompanyName LIKE :search OR c.CustomerCode LIKE :search OR c.ContactPerson1 LIKE :search OR c.EmailAddress LIKE :search)";
        $countParams['search'] = "%$search%";
    }
    if ($townshipId) {
        $countSql .= " AND c.TownshipID = :townshipId";
        $countParams['townshipId'] = $townshipId;
    }
    if ($saleTeamId) {
        $countSql .= " AND c.SaleTeamID = :saleTeamId";
        $countParams['saleTeamId'] = $saleTeamId;
    }
    if ($activeOnly && $activeOnly !== 'false') {
        $countSql .= " AND c.Active = 1";
    }
    
    $countStmt = $pdo->prepare($countSql);
    foreach ($countParams as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Count customers with location data
    $locationStmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM Customers 
        WHERE Latitude IS NOT NULL AND Longitude IS NOT NULL 
        AND Latitude != 0 AND Longitude != 0 AND Active = 1
    ");
    $locationCount = $locationStmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $response = [
        'success' => true,
        'message' => 'Customers retrieved successfully',
        'data' => $customers,
        'returned_count' => count($customers),
        'total' => (int)$total,
        'customers_with_location' => (int)$locationCount,
        'page' => $page,
        'limit' => $limit == 999999 ? 'ALL' : $limit,
        'total_pages' => $limit == 999999 ? 1 : ceil($total / $limit),
        'table_info' => [
            'customer_columns' => $customerColumns,
            'township_columns' => $townshipColumns,
            'saleteam_columns' => $saleTeamColumns,
            'township_name_field' => $townshipNameField,
            'saleteam_name_field' => $saleTeamNameField
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
