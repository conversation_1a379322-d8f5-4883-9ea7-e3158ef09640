<?php
/**
 * Customers API endpoint
 * Upload to: /api/customers/customers.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get request parameters
    $search = $_GET['search'] ?? null;
    $customerId = $_GET['customer_id'] ?? null;
    $customerCode = $_GET['customer_code'] ?? null;
    $townshipId = $_GET['township_id'] ?? null;
    $saleTeamId = $_GET['sale_team_id'] ?? null;
    $activeOnly = $_GET['active_only'] ?? true;
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 50);
    
    // If limit is very high (like 10000), get all customers
    if ($limit >= 1000) {
        $limit = 999999; // Effectively no limit
        $offset = 0;
        $page = 1;
    } else {
        $offset = ($page - 1) * $limit;
    }
    
    // Build the SQL query
    $sql = "
        SELECT
            c.CustomerID,
            c.Customer<PERSON>,
            c.<PERSON>Name,
            c.ContactPerson1,
            c.<PERSON>,
            c.<PERSON>dress,
            c.Phone,
            c.MobileNo,
            c.<PERSON>,
            c.CreditLimit,
            c.Active,
            c.TownshipID,
            t.TownshipName,
            c.SubZoneID,
            c.SaleTeamID,
            st.SaleTeam,
            c.Latitude,
            c.Longitude,
            c.CreationDate,
            c.ModifiedDate
        FROM Customers c
        LEFT JOIN Townships t ON c.TownshipID = t.TownshipID
        LEFT JOIN SaleTeam st ON c.SaleTeamID = st.SaleTeamID
        WHERE 1=1
    ";
    
    $params = [];
    
    // Add specific customer ID filter
    if ($customerId) {
        $sql .= " AND c.CustomerID = :customerId";
        $params['customerId'] = $customerId;
    }
    
    // Add specific customer code filter
    if ($customerCode) {
        $sql .= " AND c.CustomerCode = :customerCode";
        $params['customerCode'] = $customerCode;
    }
    
    // Add search filter
    if ($search) {
        $sql .= " AND (c.CompanyName LIKE :search OR c.CustomerCode LIKE :search OR c.ContactPerson1 LIKE :search OR c.EmailAddress LIKE :search)";
        $params['search'] = "%$search%";
    }

    // Add township filter
    if ($townshipId) {
        $sql .= " AND c.TownshipID = :townshipId";
        $params['townshipId'] = $townshipId;
    }

    // Add sale team filter
    if ($saleTeamId) {
        $sql .= " AND c.SaleTeamID = :saleTeamId";
        $params['saleTeamId'] = $saleTeamId;
    }

    // Add active only filter
    if ($activeOnly && $activeOnly !== 'false') {
        $sql .= " AND c.Active = 1";
    }
    
    // Add ordering and pagination
    $sql .= " ORDER BY c.CompanyName ASC LIMIT :limit OFFSET :offset";
    
    // Prepare and execute the query
    $stmt = $pdo->prepare($sql);
    
    // Bind parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $rawCustomers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format customers to ensure consistent data types
    $customers = [];
    foreach ($rawCustomers as $customer) {
        $customers[] = [
            'CustomerID' => (int)$customer['CustomerID'],
            'CustomerCode' => (string)$customer['CustomerCode'],
            'CompanyName' => (string)$customer['CompanyName'],
            'ContactPerson1' => (string)($customer['ContactPerson1'] ?? ''),
            'ContactPerson2' => (string)($customer['ContactPerson2'] ?? ''),
            'Address' => (string)($customer['Address'] ?? ''),
            'Phone' => (string)($customer['Phone'] ?? ''),
            'MobileNo' => (string)($customer['MobileNo'] ?? ''),
            'EmailAddress' => (string)($customer['EmailAddress'] ?? ''),
            'CreditLimit' => (float)($customer['CreditLimit'] ?? 0),
            'Active' => (int)$customer['Active'],
            'TownshipID' => $customer['TownshipID'] ? (int)$customer['TownshipID'] : null,
            'TownshipName' => (string)($customer['TownshipName'] ?? ''),
            'SubZoneID' => $customer['SubZoneID'] ? (int)$customer['SubZoneID'] : null,
            'SaleTeamID' => $customer['SaleTeamID'] ? (int)$customer['SaleTeamID'] : null,
            'SaleTeam' => (string)($customer['SaleTeam'] ?? ''),
            'Latitude' => $customer['Latitude'] ? (float)$customer['Latitude'] : null,
            'Longitude' => $customer['Longitude'] ? (float)$customer['Longitude'] : null,
            'CreationDate' => (string)($customer['CreationDate'] ?? ''),
            'ModifiedDate' => (string)($customer['ModifiedDate'] ?? '')
        ];
    }
    
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) as total FROM Customers c WHERE 1=1";
    $countParams = [];

    if ($customerId) {
        $countSql .= " AND c.CustomerID = :customerId";
        $countParams['customerId'] = $customerId;
    }
    if ($customerCode) {
        $countSql .= " AND c.CustomerCode = :customerCode";
        $countParams['customerCode'] = $customerCode;
    }
    if ($search) {
        $countSql .= " AND (c.CompanyName LIKE :search OR c.CustomerCode LIKE :search OR c.ContactPerson1 LIKE :search OR c.EmailAddress LIKE :search)";
        $countParams['search'] = "%$search%";
    }
    if ($townshipId) {
        $countSql .= " AND c.TownshipID = :townshipId";
        $countParams['townshipId'] = $townshipId;
    }
    if ($saleTeamId) {
        $countSql .= " AND c.SaleTeamID = :saleTeamId";
        $countParams['saleTeamId'] = $saleTeamId;
    }
    if ($activeOnly && $activeOnly !== 'false') {
        $countSql .= " AND c.Active = 1";
    }
    
    $countStmt = $pdo->prepare($countSql);
    foreach ($countParams as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Customers retrieved successfully',
        'data' => $customers,
        'returned_count' => count($customers),
        'total' => (int)$total,
        'page' => $page,
        'limit' => $limit == 999999 ? 'ALL' : $limit,
        'total_pages' => $limit == 999999 ? 1 : ceil($total / $limit)
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // Handle database errors
    $response = [
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Handle other errors
    $response = [
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
}
?>
