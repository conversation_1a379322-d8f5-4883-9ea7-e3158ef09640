<?php
/**
 * PriceList API endpoint
 * Upload to: /api/prices/prices.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get request parameters
    $productId = $_GET['product_id'] ?? null;
    $priceDescriptionId = $_GET['price_description_id'] ?? 1; // Default to 1
    $activeOnly = $_GET['active_only'] ?? true;
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 50);
    
    // If limit is very high (like 10000), get all prices
    if ($limit >= 1000) {
        $limit = 999999; // Effectively no limit
        $offset = 0;
        $page = 1;
    } else {
        $offset = ($page - 1) * $limit;
    }
    
    // Build the SQL query
    $sql = "
        SELECT 
            pl.PriceListID,
            pl.ProductID,
            p.`Product Code`,
            p.ProductName,
            pl.Price,
            pl.PriceDescription,
            pl.PriceDescriptionID,
            pl.ActivePrice,
            pl.CreatedDate,
            pl.ModifiedDate
        FROM PriceList pl
        LEFT JOIN Products p ON pl.ProductID = p.ProductID
        WHERE 1=1
    ";
    
    $params = [];
    
    // Add specific product ID filter
    if ($productId) {
        $sql .= " AND pl.ProductID = :productId";
        $params['productId'] = $productId;
    }
    
    // Add price description ID filter
    if ($priceDescriptionId) {
        $sql .= " AND pl.PriceDescriptionID = :priceDescriptionId";
        $params['priceDescriptionId'] = $priceDescriptionId;
    }
    
    // Add active only filter
    if ($activeOnly && $activeOnly !== 'false') {
        $sql .= " AND pl.ActivePrice = 1";
    }
    
    // Add ordering and pagination
    $sql .= " ORDER BY p.ProductName ASC LIMIT :limit OFFSET :offset";
    
    // Prepare and execute the query
    $stmt = $pdo->prepare($sql);
    
    // Bind parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $rawPrices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format prices to ensure consistent data types
    $prices = [];
    foreach ($rawPrices as $price) {
        $prices[] = [
            'PriceListID' => (int)$price['PriceListID'],
            'ProductID' => (int)$price['ProductID'],
            'Product Code' => (string)($price['Product Code'] ?? ''),
            'ProductName' => (string)($price['ProductName'] ?? ''),
            'Price' => (float)$price['Price'],
            'PriceDescription' => (string)($price['PriceDescription'] ?? ''),
            'PriceDescriptionID' => (int)$price['PriceDescriptionID'],
            'ActivePrice' => (int)$price['ActivePrice'],
            'CreatedDate' => (string)($price['CreatedDate'] ?? ''),
            'ModifiedDate' => (string)($price['ModifiedDate'] ?? '')
        ];
    }
    
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) as total FROM PriceList pl WHERE 1=1";
    $countParams = [];
    
    if ($productId) {
        $countSql .= " AND pl.ProductID = :productId";
        $countParams['productId'] = $productId;
    }
    if ($priceDescriptionId) {
        $countSql .= " AND pl.PriceDescriptionID = :priceDescriptionId";
        $countParams['priceDescriptionId'] = $priceDescriptionId;
    }
    if ($activeOnly && $activeOnly !== 'false') {
        $countSql .= " AND pl.ActivePrice = 1";
    }
    
    $countStmt = $pdo->prepare($countSql);
    foreach ($countParams as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Prices retrieved successfully',
        'data' => $prices,
        'returned_count' => count($prices),
        'total' => (int)$total,
        'page' => $page,
        'limit' => $limit == 999999 ? 'ALL' : $limit,
        'total_pages' => $limit == 999999 ? 1 : ceil($total / $limit),
        'filters' => [
            'price_description_id' => (int)$priceDescriptionId,
            'active_only' => $activeOnly
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // Handle database errors
    $response = [
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Handle other errors
    $response = [
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
}
?>
