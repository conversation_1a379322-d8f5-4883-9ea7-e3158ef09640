<?php
/**
 * Database configuration
 * Upload to: /api/config/database.php
 */

// Database configuration
$host = '*************';
$dbname = 'DemoROasis';
$username = 'DemoROasisUser';
$password = '!nt3ll!g3nt';

try {
    // Create PDO connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit;
}
?>
