<?php
/**
 * User Login API
 * Upload to: /api/auth/login.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    if (!isset($input['username']) || !isset($input['password'])) {
        throw new Exception('Username and password are required');
    }
    
    $username = trim($input['username']);
    $password = trim($input['password']);
    
    if (empty($username) || empty($password)) {
        throw new Exception('Username and password cannot be empty');
    }
    
    // Check user credentials
    $sql = "
        SELECT 
            UserID,
            UserName,
            Password,
            SaleTeamID,
            ActiveUser,
            EmployeeID
        FROM Users 
        WHERE UserName = :username 
        AND ActiveUser = 1
        LIMIT 1
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['username' => $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('Invalid username or user is not active');
    }
    
    // Verify password (assuming plain text for now, should be hashed in production)
    if ($user['Password'] !== $password) {
        throw new Exception('Invalid password');
    }
    
    // Get SaleTeam information
    $saleTeamSql = "
        SELECT 
            SaleTeamID,
            SaleTeam as SaleTeamName
        FROM SaleTeam 
        WHERE SaleTeamID = :saleTeamId
        LIMIT 1
    ";
    
    $saleTeamStmt = $pdo->prepare($saleTeamSql);
    $saleTeamStmt->execute(['saleTeamId' => $user['SaleTeamID']]);
    $saleTeam = $saleTeamStmt->fetch(PDO::FETCH_ASSOC);
    
    // Prepare response data
    $userData = [
        'UserID' => (int)$user['UserID'],
        'UserName' => (string)$user['UserName'],
        'EmployeeID' => (int)($user['EmployeeID'] ?? 0),
        'SaleTeamID' => (int)($user['SaleTeamID'] ?? 0),
        'SaleTeamName' => (string)($saleTeam['SaleTeamName'] ?? ''),
        'ActiveUser' => (int)$user['ActiveUser']
    ];
    
    // Generate a simple session token (in production, use proper JWT or session management)
    $sessionToken = base64_encode($user['UserID'] . '_' . time() . '_' . $user['UserName']);
    
    $response = [
        'success' => true,
        'message' => 'Login successful',
        'data' => [
            'user' => $userData,
            'session_token' => $sessionToken,
            'login_time' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Login failed: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
