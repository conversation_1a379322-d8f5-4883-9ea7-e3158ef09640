<?php
/**
 * Dynamic Townships API endpoint - adapts to actual table structure
 * Upload to: /api/townships/townships_dynamic.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // First, check what columns exist in the Townships table
    $stmt = $pdo->query("DESCRIBE Townships");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $columnNames = array_column($columns, 'Field');
    
    // Determine the correct field names
    $idField = 'TownshipID'; // This should be standard
    $nameField = null;

    // Check for name field variations
    if (in_array('TownshipName', $columnNames)) {
        $nameField = 'TownshipName';
    } elseif (in_array('Township', $columnNames)) {
        $nameField = 'Township';
    } elseif (in_array('Name', $columnNames)) {
        $nameField = 'Name';
    }
    
    if (!$nameField) {
        throw new Exception("Could not find name field in Townships table. Available columns: " . implode(', ', $columnNames));
    }
    
    // Build dynamic SQL query
    $sql = "
        SELECT
            t.$idField,
            t.$nameField,
            COUNT(c.CustomerID) as CustomerCount
        FROM Townships t
        LEFT JOIN Customers c ON t.$idField = c.TownshipID AND c.Active = 1
        GROUP BY t.$idField, t.$nameField
        ORDER BY t.$nameField ASC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $rawTownships = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format townships with standardized field names
    $townships = [];
    foreach ($rawTownships as $township) {
        $townships[] = [
            'TownshipID' => (int)$township[$idField],
            'TownshipName' => (string)$township[$nameField],
            'CustomerCount' => (int)$township['CustomerCount']
        ];
    }
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Townships retrieved successfully',
        'data' => $townships,
        'total' => count($townships),
        'table_info' => [
            'columns_found' => $columnNames,
            'id_field' => $idField,
            'name_field' => $nameField
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
