<?php
/**
 * Townships API endpoint
 * Upload to: /api/townships/townships.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get all townships
    $sql = "
        SELECT
            t.TownshipID,
            t.TownshipName,
            COUNT(c.CustomerID) as CustomerCount
        FROM Townships t
        LEFT JOIN Customers c ON t.TownshipID = c.TownshipID AND c.Active = 1
        GROUP BY t.TownshipID, t.TownshipName
        ORDER BY t.TownshipName ASC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $rawTownships = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format townships to ensure consistent data types
    $townships = [];
    foreach ($rawTownships as $township) {
        $townships[] = [
            'TownshipID' => (int)$township['TownshipID'],
            'TownshipName' => (string)$township['TownshipName'],
            'CustomerCount' => (int)$township['CustomerCount']
        ];
    }
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Townships retrieved successfully',
        'data' => $townships,
        'total' => count($townships)
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
