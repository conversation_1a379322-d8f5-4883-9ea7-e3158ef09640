<?php
/**
 * Check Users table structure for login functionality
 * Upload to: /api/test/check_users_table.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    $response = [
        'success' => true,
        'message' => 'Users table structure analysis',
        'tables' => []
    ];
    
    // Check Users table structure
    try {
        $stmt = $pdo->query("DESCRIBE Users");
        $usersColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['tables']['Users'] = [
            'exists' => true,
            'columns' => $usersColumns,
            'column_names' => array_column($usersColumns, 'Field')
        ];
        
        // Get sample data (without passwords for security)
        $stmt = $pdo->query("SELECT UserID, UserName, SaleTeamID, ActiveUser FROM Users LIMIT 5");
        $sampleData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['Users']['sample_data'] = $sampleData;
        
        // Count active users
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM Users WHERE ActiveUser = 1");
        $activeCount = $stmt->fetch(PDO::FETCH_ASSOC);
        $response['active_users_count'] = (int)$activeCount['count'];
        
    } catch (Exception $e) {
        $response['tables']['Users'] = [
            'exists' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Re-check OrderRequest table with updated requirements
    try {
        $stmt = $pdo->query("DESCRIBE OrderRequest");
        $orderRequestColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['tables']['OrderRequest_Updated'] = [
            'exists' => true,
            'columns' => $orderRequestColumns,
            'column_names' => array_column($orderRequestColumns, 'Field')
        ];
        
    } catch (Exception $e) {
        $response['tables']['OrderRequest_Updated'] = [
            'exists' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Re-check OrderRequestDetails table with updated requirements
    try {
        $stmt = $pdo->query("DESCRIBE OrderRequestDetails");
        $orderDetailsColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['tables']['OrderRequestDetails_Updated'] = [
            'exists' => true,
            'columns' => $orderDetailsColumns,
            'column_names' => array_column($orderDetailsColumns, 'Field')
        ];
        
    } catch (Exception $e) {
        $response['tables']['OrderRequestDetails_Updated'] = [
            'exists' => false,
            'error' => $e->getMessage()
        ];
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
