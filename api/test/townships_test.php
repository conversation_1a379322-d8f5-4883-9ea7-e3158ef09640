<?php
/**
 * Townships Table Test API
 * Upload to: /api/test/townships_test.php
 * This will test the Townships table structure and data
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    $response = [
        'success' => true,
        'message' => 'Townships table analysis',
        'tests' => []
    ];
    
    // Test 1: Check if Townships table exists and get structure
    try {
        $stmt = $pdo->query("DESCRIBE Townships");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $columnNames = array_column($columns, 'Field');
        
        $response['tests']['table_structure'] = [
            'success' => true,
            'columns' => $columns,
            'column_names' => $columnNames
        ];
        
    } catch (Exception $e) {
        $response['tests']['table_structure'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 2: Get sample data from Townships
    try {
        $stmt = $pdo->query("SELECT * FROM Townships LIMIT 10");
        $sampleData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['tests']['sample_data'] = [
            'success' => true,
            'count' => count($sampleData),
            'data' => $sampleData
        ];
        
    } catch (Exception $e) {
        $response['tests']['sample_data'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 3: Count total townships
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM Townships");
        $total = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $response['tests']['total_count'] = [
            'success' => true,
            'total_townships' => (int)$total['total']
        ];
        
    } catch (Exception $e) {
        $response['tests']['total_count'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 4: Test JOIN with Customers table
    try {
        $stmt = $pdo->query("
            SELECT 
                t.TownshipID,
                t.TownshipName,
                COUNT(c.CustomerID) as CustomerCount
            FROM Townships t
            LEFT JOIN Customers c ON t.TownshipID = c.TownshipID AND c.Active = 1
            GROUP BY t.TownshipID, t.TownshipName
            ORDER BY t.TownshipName ASC
            LIMIT 10
        ");
        $joinData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['tests']['join_test'] = [
            'success' => true,
            'data' => $joinData
        ];
        
    } catch (Exception $e) {
        $response['tests']['join_test'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 5: Check customers with township data
    try {
        $stmt = $pdo->query("
            SELECT 
                COUNT(*) as total_customers,
                COUNT(TownshipID) as customers_with_township,
                COUNT(DISTINCT TownshipID) as unique_townships_used
            FROM Customers 
            WHERE Active = 1
        ");
        $customerStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $response['tests']['customer_township_stats'] = [
            'success' => true,
            'total_active_customers' => (int)$customerStats['total_customers'],
            'customers_with_township' => (int)$customerStats['customers_with_township'],
            'unique_townships_used' => (int)$customerStats['unique_townships_used']
        ];
        
    } catch (Exception $e) {
        $response['tests']['customer_township_stats'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 6: Try different field name variations
    $fieldTests = [];
    $possibleFields = ['TownshipName', 'Township', 'Name'];

    foreach ($possibleFields as $field) {
        try {
            $stmt = $pdo->query("SELECT $field FROM Townships LIMIT 1");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $fieldTests[$field] = [
                'exists' => true,
                'sample_value' => $result[$field] ?? null
            ];
        } catch (Exception $e) {
            $fieldTests[$field] = [
                'exists' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    $response['tests']['field_variations'] = $fieldTests;
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
