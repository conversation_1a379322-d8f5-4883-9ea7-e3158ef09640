<?php
/**
 * Test API that returns customers with guaranteed location data
 * Upload to: /api/test/customers_with_location.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get customers with actual location data from your database
    $sql = "
        SELECT 
            c.CustomerID,
            c.CustomerCode,
            c.CompanyName,
            c.ContactPerson1,
            c.ContactPerson2,
            c.Address,
            c.Phone,
            c.MobileNo,
            c.EmailAddress,
            c.CreditLimit,
            c.Active,
            c.TownshipID,
            t.TownshipName,
            c.SaleTeamID,
            c.Latitude,
            c.Longitude,
            c.CreationDate,
            c.ModifiedDate
        FROM Customers c
        LEFT JOIN Townships t ON c.TownshipID = t.TownshipID
        WHERE c.Latitude IS NOT NULL 
        AND c.Longitude IS NOT NULL 
        AND c.Latitude != 0 
        AND c.Longitude != 0 
        AND c.Active = 1
        ORDER BY c.CompanyName ASC
        LIMIT 10
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $rawCustomers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // If no customers with real location data, create test data
    if (empty($rawCustomers)) {
        // Get some customers and add test coordinates
        $sql = "
            SELECT 
                c.CustomerID,
                c.CustomerCode,
                c.CompanyName,
                c.ContactPerson1,
                c.ContactPerson2,
                c.Address,
                c.Phone,
                c.MobileNo,
                c.EmailAddress,
                c.CreditLimit,
                c.Active,
                c.TownshipID,
                t.TownshipName,
                c.SaleTeamID,
                c.CreationDate,
                c.ModifiedDate
            FROM Customers c
            LEFT JOIN Townships t ON c.TownshipID = t.TownshipID
            WHERE c.Active = 1
            ORDER BY c.CompanyName ASC
            LIMIT 5
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $rawCustomers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Add test coordinates (Myanmar locations)
        $testCoordinates = [
            ['lat' => 16.866069, 'lng' => 96.195132], // Yangon
            ['lat' => 21.969924, 'lng' => 96.233467], // Mandalay
            ['lat' => 19.745161, 'lng' => 96.129150], // Bagan
            ['lat' => 22.000000, 'lng' => 96.083333], // Pyin Oo Lwin
            ['lat' => 16.833333, 'lng' => 96.166667]  // Yangon suburbs
        ];
        
        foreach ($rawCustomers as $index => &$customer) {
            $coords = $testCoordinates[$index % count($testCoordinates)];
            $customer['Latitude'] = $coords['lat'];
            $customer['Longitude'] = $coords['lng'];
        }
    }
    
    // Format customers
    $customers = [];
    foreach ($rawCustomers as $customer) {
        $customers[] = [
            'CustomerID' => (int)$customer['CustomerID'],
            'CustomerCode' => (string)$customer['CustomerCode'],
            'CompanyName' => (string)$customer['CompanyName'],
            'ContactPerson1' => (string)($customer['ContactPerson1'] ?? ''),
            'ContactPerson2' => (string)($customer['ContactPerson2'] ?? ''),
            'Address' => (string)($customer['Address'] ?? ''),
            'Phone' => (string)($customer['Phone'] ?? ''),
            'MobileNo' => (string)($customer['MobileNo'] ?? ''),
            'EmailAddress' => (string)($customer['EmailAddress'] ?? ''),
            'CreditLimit' => (float)($customer['CreditLimit'] ?? 0),
            'Active' => (int)$customer['Active'],
            'TownshipID' => $customer['TownshipID'] ? (int)$customer['TownshipID'] : null,
            'TownshipName' => (string)($customer['TownshipName'] ?? ''),
            'SaleTeamID' => $customer['SaleTeamID'] ? (int)$customer['SaleTeamID'] : null,
            'SaleTeam' => '',
            'Latitude' => (float)$customer['Latitude'],
            'Longitude' => (float)$customer['Longitude'],
            'CreationDate' => (string)($customer['CreationDate'] ?? ''),
            'ModifiedDate' => (string)($customer['ModifiedDate'] ?? '')
        ];
    }
    
    $response = [
        'success' => true,
        'message' => 'Test customers with location data',
        'data' => $customers,
        'returned_count' => count($customers),
        'total' => count($customers),
        'note' => empty($rawCustomers) ? 'Using test coordinates' : 'Using real database coordinates'
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
