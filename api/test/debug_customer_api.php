<?php
/**
 * Debug Customer API Response
 * Upload to: /api/test/debug_customer_api.php
 * This will show exactly what the customers API is returning
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Test the exact same query as the customers API
    $sql = "
        SELECT 
            c.CustomerID,
            c.CustomerCode,
            c.CompanyName,
            c.ContactPerson1,
            c.ContactPerson2,
            c.Address,
            c.Phone,
            c.MobileNo,
            c.EmailAddress,
            c.CreditLimit,
            c.Active,
            c.TownshipID,
            t.TownshipName,
            c.SaleTeamID,
            c.Latitude,
            c.Longitude,
            c.CreationDate,
            c.ModifiedDate
        FROM Customers c
        LEFT JOIN Townships t ON c.TownshipID = t.TownshipID
        WHERE c.Active = 1
        ORDER BY c.CompanyName ASC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $rawCustomers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $response = [
        'success' => true,
        'message' => 'Debug customer API response',
        'raw_database_data' => $rawCustomers,
        'analysis' => []
    ];
    
    // Analyze each customer
    foreach ($rawCustomers as $index => $customer) {
        $analysis = [
            'customer_name' => $customer['CompanyName'],
            'latitude_raw' => $customer['Latitude'],
            'longitude_raw' => $customer['Longitude'],
            'latitude_type' => gettype($customer['Latitude']),
            'longitude_type' => gettype($customer['Longitude']),
            'latitude_is_null' => is_null($customer['Latitude']),
            'longitude_is_null' => is_null($customer['Longitude']),
            'latitude_is_zero' => ($customer['Latitude'] == 0),
            'longitude_is_zero' => ($customer['Longitude'] == 0),
            'has_valid_location' => (
                !is_null($customer['Latitude']) && 
                !is_null($customer['Longitude']) && 
                $customer['Latitude'] != 0 && 
                $customer['Longitude'] != 0
            )
        ];
        
        $response['analysis'][] = $analysis;
    }
    
    // Format as the API would return
    $formattedCustomers = [];
    foreach ($rawCustomers as $customer) {
        $formattedCustomers[] = [
            'CustomerID' => (int)$customer['CustomerID'],
            'CustomerCode' => (string)$customer['CustomerCode'],
            'CompanyName' => (string)$customer['CompanyName'],
            'ContactPerson1' => (string)($customer['ContactPerson1'] ?? ''),
            'ContactPerson2' => (string)($customer['ContactPerson2'] ?? ''),
            'Address' => (string)($customer['Address'] ?? ''),
            'Phone' => (string)($customer['Phone'] ?? ''),
            'MobileNo' => (string)($customer['MobileNo'] ?? ''),
            'EmailAddress' => (string)($customer['EmailAddress'] ?? ''),
            'CreditLimit' => (float)($customer['CreditLimit'] ?? 0),
            'Active' => (int)$customer['Active'],
            'TownshipID' => $customer['TownshipID'] ? (int)$customer['TownshipID'] : null,
            'TownshipName' => (string)($customer['TownshipName'] ?? ''),
            'SaleTeamID' => $customer['SaleTeamID'] ? (int)$customer['SaleTeamID'] : null,
            'SaleTeam' => '',
            'Latitude' => $customer['Latitude'] ? (float)$customer['Latitude'] : null,
            'Longitude' => $customer['Longitude'] ? (float)$customer['Longitude'] : null,
            'CreationDate' => (string)($customer['CreationDate'] ?? ''),
            'ModifiedDate' => (string)($customer['ModifiedDate'] ?? '')
        ];
    }
    
    $response['formatted_api_response'] = $formattedCustomers;
    
    // Count customers with location
    $locationCount = 0;
    foreach ($formattedCustomers as $customer) {
        if ($customer['Latitude'] !== null && $customer['Longitude'] !== null && 
            $customer['Latitude'] != 0 && $customer['Longitude'] != 0) {
            $locationCount++;
        }
    }
    
    $response['summary'] = [
        'total_customers_checked' => count($rawCustomers),
        'customers_with_location' => $locationCount,
        'customers_without_location' => count($rawCustomers) - $locationCount
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
