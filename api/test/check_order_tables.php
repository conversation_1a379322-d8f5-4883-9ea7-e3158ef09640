<?php
/**
 * Check OrderRequest and OrderRequestDetails table structure
 * Upload to: /api/test/check_order_tables.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    $response = [
        'success' => true,
        'message' => 'Order tables structure analysis',
        'tables' => []
    ];
    
    // Check OrderRequest table structure
    try {
        $stmt = $pdo->query("DESCRIBE OrderRequest");
        $orderRequestColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['tables']['OrderRequest'] = [
            'exists' => true,
            'columns' => $orderRequestColumns,
            'column_names' => array_column($orderRequestColumns, 'Field')
        ];
        
        // Get sample data
        $stmt = $pdo->query("SELECT * FROM OrderRequest LIMIT 3");
        $sampleData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['OrderRequest']['sample_data'] = $sampleData;
        
    } catch (Exception $e) {
        $response['tables']['OrderRequest'] = [
            'exists' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Check OrderRequestDetails table structure
    try {
        $stmt = $pdo->query("DESCRIBE OrderRequestDetails");
        $orderDetailsColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['tables']['OrderRequestDetails'] = [
            'exists' => true,
            'columns' => $orderDetailsColumns,
            'column_names' => array_column($orderDetailsColumns, 'Field')
        ];
        
        // Get sample data
        $stmt = $pdo->query("SELECT * FROM OrderRequestDetails LIMIT 3");
        $sampleData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['OrderRequestDetails']['sample_data'] = $sampleData;
        
    } catch (Exception $e) {
        $response['tables']['OrderRequestDetails'] = [
            'exists' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Check if there are any existing orders to understand the structure
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM OrderRequest");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        $response['existing_orders_count'] = (int)$count['count'];
    } catch (Exception $e) {
        $response['existing_orders_count'] = 'Error: ' . $e->getMessage();
    }
    
    // Test a simple insert to see what fields are required
    try {
        $testSql = "INSERT INTO OrderRequest (OrderCollectionNumber, CustomerCode, StationID) VALUES ('TEST_001', 'TEST', 1)";
        // Don't actually execute, just prepare to see if it works
        $stmt = $pdo->prepare($testSql);
        $response['test_insert'] = [
            'sql' => $testSql,
            'status' => 'SQL prepared successfully - basic fields exist'
        ];
    } catch (Exception $e) {
        $response['test_insert'] = [
            'sql' => $testSql,
            'error' => $e->getMessage()
        ];
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
