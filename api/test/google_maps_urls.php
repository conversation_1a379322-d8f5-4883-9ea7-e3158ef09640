<?php
/**
 * Test Google Maps URL formats with customer names
 * Upload to: /api/test/google_maps_urls.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Get a few customers with location data
    $sql = "
        SELECT 
            c.CustomerID,
            c.CustomerCode,
            c.CompanyName,
            c.Latitude,
            c.Longitude
        FROM Customers c
        WHERE c.Latitude IS NOT NULL 
        AND c.Longitude IS NOT NULL 
        AND c.Latitude != 0 
        AND c.Longitude != 0 
        AND c.Active = 1
        ORDER BY c.CompanyName ASC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // If no real location data, create test data
    if (empty($customers)) {
        $customers = [
            [
                'CustomerID' => 1,
                'CustomerCode' => 'TEST001',
                'CompanyName' => 'ABC Trading Company Ltd',
                'Latitude' => 16.866069,
                'Longitude' => 96.195132
            ],
            [
                'CustomerID' => 2,
                'CustomerCode' => 'TEST002',
                'CompanyName' => 'XYZ Manufacturing Co',
                'Latitude' => 21.969924,
                'Longitude' => 96.233467
            ]
        ];
    }
    
    $response = [
        'success' => true,
        'message' => 'Google Maps URL formats test',
        'customers' => []
    ];
    
    foreach ($customers as $customer) {
        $customerName = $customer['CompanyName'];
        $lat = (float)$customer['Latitude'];
        $lng = (float)$customer['Longitude'];
        $encodedName = urlencode($customerName);
        
        $urlFormats = [
            'place_url' => "https://www.google.com/maps/place/$encodedName/@$lat,$lng,17z",
            'search_url' => "https://www.google.com/maps/search/$encodedName/@$lat,$lng,17z",
            'query_with_label' => "https://maps.google.com/?q=$lat,$lng&label=$encodedName",
            'query_with_name' => "https://www.google.com/maps?q=$lat,$lng($encodedName)",
            'geo_uri' => "geo:$lat,$lng?q=$lat,$lng($encodedName)",
            'simple_query' => "https://www.google.com/maps?q=$lat,$lng"
        ];
        
        $response['customers'][] = [
            'customer_info' => [
                'id' => (int)$customer['CustomerID'],
                'code' => $customer['CustomerCode'],
                'name' => $customerName,
                'latitude' => $lat,
                'longitude' => $lng
            ],
            'map_urls' => $urlFormats,
            'test_instructions' => [
                'place_url' => 'Best for showing custom place names - try this first',
                'search_url' => 'Search-based approach - good fallback',
                'query_with_label' => 'Uses label parameter - may work in some apps',
                'query_with_name' => 'Name in parentheses - basic approach',
                'geo_uri' => 'Mobile app geo URI - for native map apps',
                'simple_query' => 'Coordinates only - fallback option'
            ]
        ];
    }
    
    $response['usage_instructions'] = [
        'step1' => 'Copy any URL from the map_urls section',
        'step2' => 'Paste it in your browser to test',
        'step3' => 'Check if the customer name appears as a label/marker',
        'step4' => 'The place_url format usually works best for custom labels'
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
