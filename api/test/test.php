<?php
/**
 * Test API endpoint
 * Upload to: /api/test/test.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Test database connection and get basic info
    $response = [
        'success' => true,
        'message' => 'API is working correctly',
        'server_info' => [
            'timestamp' => date('Y-m-d H:i:s'),
            'php_version' => phpversion(),
            'server' => $_SERVER['SERVER_NAME'] ?? 'Unknown'
        ],
        'database_info' => [
            'status' => 'connected',
            'host' => '*************',
            'database' => 'DemoROasis'
        ]
    ];
    
    // Get table counts
    $tables = ['Products', 'Categories', 'Principals', 'Customers', 'OrderRequest'];
    $tableCounts = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $tableCounts[$table] = (int)$result['count'];
        } catch (Exception $e) {
            $tableCounts[$table] = 'Error: ' . $e->getMessage();
        }
    }
    
    $response['table_counts'] = $tableCounts;
    
    // Test sample product
    $stmt = $pdo->query("
        SELECT 
            p.ProductID,
            p.ProductName,
            p.`Selling Price`,
            p.ProductImagePath
        FROM Products p 
        WHERE p.Discontinued = 0 
        LIMIT 1
    ");
    $sampleProduct = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sampleProduct) {
        $response['sample_product'] = $sampleProduct;
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Test failed: ' . $e->getMessage(),
        'server_info' => [
            'timestamp' => date('Y-m-d H:i:s'),
            'php_version' => phpversion()
        ]
    ], JSON_PRETTY_PRINT);
}
?>
