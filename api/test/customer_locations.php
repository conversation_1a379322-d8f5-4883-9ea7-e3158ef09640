<?php
/**
 * Customer Locations Test API
 * Upload to: /api/test/customer_locations.php
 * This will test customer location data in your database
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    $response = [
        'success' => true,
        'message' => 'Customer location data analysis',
        'tests' => []
    ];
    
    // Test 1: Check total customers
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM Customers WHERE Active = 1");
        $total = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $response['tests']['total_customers'] = [
            'success' => true,
            'total_active_customers' => (int)$total['total']
        ];
        
    } catch (Exception $e) {
        $response['tests']['total_customers'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 2: Check customers with location data
    try {
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM Customers 
            WHERE Latitude IS NOT NULL 
            AND Longitude IS NOT NULL 
            AND Latitude != 0 
            AND Longitude != 0 
            AND Active = 1
        ");
        $withLocation = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $response['tests']['customers_with_location'] = [
            'success' => true,
            'customers_with_valid_location' => (int)$withLocation['count']
        ];
        
    } catch (Exception $e) {
        $response['tests']['customers_with_location'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 3: Get sample customers with location data
    try {
        $stmt = $pdo->query("
            SELECT 
                CustomerID,
                CustomerCode,
                CompanyName,
                Latitude,
                Longitude,
                TownshipID
            FROM Customers 
            WHERE Latitude IS NOT NULL 
            AND Longitude IS NOT NULL 
            AND Latitude != 0 
            AND Longitude != 0 
            AND Active = 1
            LIMIT 10
        ");
        $sampleCustomers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['tests']['sample_customers_with_location'] = [
            'success' => true,
            'count' => count($sampleCustomers),
            'customers' => $sampleCustomers
        ];
        
    } catch (Exception $e) {
        $response['tests']['sample_customers_with_location'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 4: Check location data ranges (to validate coordinates)
    try {
        $stmt = $pdo->query("
            SELECT 
                MIN(Latitude) as min_lat,
                MAX(Latitude) as max_lat,
                MIN(Longitude) as min_lng,
                MAX(Longitude) as max_lng,
                AVG(Latitude) as avg_lat,
                AVG(Longitude) as avg_lng
            FROM Customers 
            WHERE Latitude IS NOT NULL 
            AND Longitude IS NOT NULL 
            AND Latitude != 0 
            AND Longitude != 0 
            AND Active = 1
        ");
        $locationStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $response['tests']['location_statistics'] = [
            'success' => true,
            'min_latitude' => (float)$locationStats['min_lat'],
            'max_latitude' => (float)$locationStats['max_lat'],
            'min_longitude' => (float)$locationStats['min_lng'],
            'max_longitude' => (float)$locationStats['max_lng'],
            'avg_latitude' => (float)$locationStats['avg_lat'],
            'avg_longitude' => (float)$locationStats['avg_lng']
        ];
        
        // Validate if coordinates look like Myanmar coordinates
        $avgLat = (float)$locationStats['avg_lat'];
        $avgLng = (float)$locationStats['avg_lng'];
        
        $isMyanmarRange = ($avgLat >= 9.0 && $avgLat <= 29.0) && ($avgLng >= 92.0 && $avgLng <= 102.0);
        
        $response['tests']['location_validation'] = [
            'coordinates_look_valid' => $isMyanmarRange,
            'note' => $isMyanmarRange ? 'Coordinates appear to be in Myanmar range' : 'Coordinates may not be in Myanmar range'
        ];
        
    } catch (Exception $e) {
        $response['tests']['location_statistics'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test 5: Test the customers API with location data
    try {
        $stmt = $pdo->query("
            SELECT 
                c.CustomerID,
                c.CustomerCode,
                c.CompanyName,
                c.Latitude,
                c.Longitude,
                t.TownshipName
            FROM Customers c
            LEFT JOIN Townships t ON c.TownshipID = t.TownshipID
            WHERE c.Latitude IS NOT NULL 
            AND c.Longitude IS NOT NULL 
            AND c.Latitude != 0 
            AND c.Longitude != 0 
            AND c.Active = 1
            LIMIT 5
        ");
        $apiTestData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format like the actual API
        $formattedCustomers = [];
        foreach ($apiTestData as $customer) {
            $formattedCustomers[] = [
                'CustomerID' => (int)$customer['CustomerID'],
                'CustomerCode' => (string)$customer['CustomerCode'],
                'CompanyName' => (string)$customer['CompanyName'],
                'TownshipName' => (string)($customer['TownshipName'] ?? ''),
                'Latitude' => (float)$customer['Latitude'],
                'Longitude' => (float)$customer['Longitude'],
                'HasLocation' => true,
                'GoogleMapsUrl' => "https://www.google.com/maps?q=" . $customer['Latitude'] . "," . $customer['Longitude']
            ];
        }
        
        $response['tests']['api_format_test'] = [
            'success' => true,
            'sample_api_response' => $formattedCustomers
        ];
        
    } catch (Exception $e) {
        $response['tests']['api_format_test'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
