<?php
/**
 * Table Structure Test API
 * Upload to: /api/test/table_structure.php
 * This will show the actual structure of your tables
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    $response = [
        'success' => true,
        'message' => 'Table structure analysis',
        'tables' => []
    ];
    
    // Check SaleTeam table structure
    try {
        $stmt = $pdo->query("DESCRIBE SaleTeam");
        $saleTeamColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['SaleTeam'] = [
            'exists' => true,
            'columns' => $saleTeamColumns
        ];
        
        // Get sample data
        $stmt = $pdo->query("SELECT * FROM SaleTeam LIMIT 3");
        $saleTeamSample = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['SaleTeam']['sample_data'] = $saleTeamSample;
        
    } catch (Exception $e) {
        $response['tables']['SaleTeam'] = [
            'exists' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Check Townships table structure
    try {
        $stmt = $pdo->query("DESCRIBE Townships");
        $townshipColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['Townships'] = [
            'exists' => true,
            'columns' => $townshipColumns
        ];
        
        // Get sample data
        $stmt = $pdo->query("SELECT * FROM Townships LIMIT 3");
        $townshipSample = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['Townships']['sample_data'] = $townshipSample;
        
    } catch (Exception $e) {
        $response['tables']['Townships'] = [
            'exists' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Check Customers table structure (relevant fields)
    try {
        $stmt = $pdo->query("DESCRIBE Customers");
        $customerColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['Customers'] = [
            'exists' => true,
            'columns' => $customerColumns
        ];
        
        // Get sample customer with location data
        $stmt = $pdo->query("
            SELECT 
                CustomerID, CustomerCode, CompanyName, 
                TownshipID, SaleTeamID, 
                Latitude, Longitude 
            FROM Customers 
            WHERE Latitude IS NOT NULL AND Longitude IS NOT NULL 
            LIMIT 3
        ");
        $customerSample = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['tables']['Customers']['sample_with_location'] = $customerSample;
        
        // Count customers with location data
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM Customers 
            WHERE Latitude IS NOT NULL AND Longitude IS NOT NULL 
            AND Latitude != 0 AND Longitude != 0
        ");
        $locationCount = $stmt->fetch(PDO::FETCH_ASSOC);
        $response['tables']['Customers']['customers_with_location'] = (int)$locationCount['count'];
        
    } catch (Exception $e) {
        $response['tables']['Customers'] = [
            'exists' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // Test JOIN query
    try {
        $stmt = $pdo->query("
            SELECT 
                c.CustomerID,
                c.CompanyName,
                c.TownshipID,
                t.TownshipName,
                c.SaleTeamID,
                st.SaleTeam,
                c.Latitude,
                c.Longitude
            FROM Customers c
            LEFT JOIN Townships t ON c.TownshipID = t.TownshipID
            LEFT JOIN SaleTeam st ON c.SaleTeamID = st.SaleTeamID
            WHERE c.Active = 1
            LIMIT 5
        ");
        $joinTest = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['join_test'] = [
            'success' => true,
            'sample_data' => $joinTest
        ];
        
    } catch (Exception $e) {
        $response['join_test'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
