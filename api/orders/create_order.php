<?php
/**
 * Create Order API - Insert to OrderRequest & OrderRequestDetails
 * Upload to: /api/orders/create_order.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $requiredFields = ['customerCode', 'stationId', 'orderItems'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }
    
    $customerCode = $input['customerCode'];
    $stationId = (int)$input['stationId'];
    $orderItems = $input['orderItems'];
    $remarks = $input['remarks'] ?? '';
    $totalAmount = (float)($input['totalAmount'] ?? 0);
    
    // Validate order items
    if (empty($orderItems) || !is_array($orderItems)) {
        throw new Exception('Order items cannot be empty');
    }
    
    // Generate OrderCollectionNumber: CustomerCode_yyyyMMddhhmmss
    $timestamp = date('YmdHis');
    $orderCollectionNumber = $customerCode . '_' . $timestamp;
    
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        // Insert into OrderRequest table
        $orderRequestSql = "
            INSERT INTO OrderRequest (
                OrderCollectionNumber,
                CustomerCode,
                StationID,
                OrderDate,
                TotalAmount,
                Remarks,
                Status,
                CreatedDate,
                ModifiedDate
            ) VALUES (
                :orderCollectionNumber,
                :customerCode,
                :stationId,
                NOW(),
                :totalAmount,
                :remarks,
                'Pending',
                NOW(),
                NOW()
            )
        ";
        
        $orderRequestStmt = $pdo->prepare($orderRequestSql);
        $orderRequestStmt->execute([
            'orderCollectionNumber' => $orderCollectionNumber,
            'customerCode' => $customerCode,
            'stationId' => $stationId,
            'totalAmount' => $totalAmount,
            'remarks' => $remarks
        ]);
        
        $orderRequestId = $pdo->lastInsertId();
        
        if (!$orderRequestId) {
            throw new Exception('Failed to create order request');
        }
        
        // Insert order items into OrderRequestDetails table
        $detailsSql = "
            INSERT INTO OrderRequestDetails (
                OrderRequestID,
                ProductCode,
                ProductName,
                Quantity,
                UnitPrice,
                TotalPrice,
                CreatedDate,
                ModifiedDate
            ) VALUES (
                :orderRequestId,
                :productCode,
                :productName,
                :quantity,
                :unitPrice,
                :totalPrice,
                NOW(),
                NOW()
            )
        ";
        
        $detailsStmt = $pdo->prepare($detailsSql);
        $insertedDetails = [];
        
        foreach ($orderItems as $item) {
            // Validate item fields
            if (!isset($item['productCode']) || !isset($item['quantity']) || !isset($item['unitPrice'])) {
                throw new Exception('Invalid order item: missing required fields');
            }
            
            $productCode = $item['productCode'];
            $productName = $item['productName'] ?? '';
            $quantity = (float)$item['quantity'];
            $unitPrice = (float)$item['unitPrice'];
            $totalPrice = $quantity * $unitPrice;
            
            $detailsStmt->execute([
                'orderRequestId' => $orderRequestId,
                'productCode' => $productCode,
                'productName' => $productName,
                'quantity' => $quantity,
                'unitPrice' => $unitPrice,
                'totalPrice' => $totalPrice
            ]);
            
            $detailId = $pdo->lastInsertId();
            
            $insertedDetails[] = [
                'OrderRequestDetailID' => (int)$detailId,
                'ProductCode' => $productCode,
                'ProductName' => $productName,
                'Quantity' => $quantity,
                'UnitPrice' => $unitPrice,
                'TotalPrice' => $totalPrice
            ];
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Return success response
        $response = [
            'success' => true,
            'message' => 'Order created successfully',
            'data' => [
                'OrderRequestID' => (int)$orderRequestId,
                'OrderCollectionNumber' => $orderCollectionNumber,
                'CustomerCode' => $customerCode,
                'StationID' => $stationId,
                'TotalAmount' => $totalAmount,
                'Status' => 'Pending',
                'OrderItems' => $insertedDetails,
                'ItemCount' => count($insertedDetails)
            ]
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error creating order: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
