<?php
/**
 * Create Order API - Insert to OrderRequest & OrderRequestDetails
 * Upload to: /api/orders/create_order.php
 */

require_once '../config/database.php';
require_once '../config/cors.php';

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $requiredFields = ['customerCode', 'customerID', 'employeeID', 'salesTeamID', 'orderItems'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }

    $customerCode = $input['customerCode'];
    $customerID = (int)$input['customerID'];
    $employeeID = (int)($input['employeeID'] ?? 0);
    $salesTeamID = (int)($input['salesTeamID'] ?? 0);
    $orderItems = $input['orderItems'];
    $remarks = $input['remarks'] ?? '';
    $stationNo = $input['stationNo'] ?? 'ST01';
    
    // Validate order items
    if (empty($orderItems) || !is_array($orderItems)) {
        throw new Exception('Order items cannot be empty');
    }
    
    // Generate OrderCollectionNumber: CustomerCode_yyyyMMddhhmmss
    $timestamp = date('YmdHis');
    $orderCollectionNumber = $customerCode . '_' . $timestamp;
    
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        // Insert into OrderRequest table (with updated required fields)
        $orderRequestSql = "
            INSERT INTO OrderRequest (
                OrderCollectionNumber,
                CustomerID,
                CustomerCode,
                OrderRequestDate,
                StationNo,
                EmployeeID,
                SalesTeamID,
                InvoiceNotes
            ) VALUES (
                :orderCollectionNumber,
                :customerID,
                :customerCode,
                NOW(),
                :stationNo,
                :employeeID,
                :salesTeamID,
                :remarks
            )
        ";

        $orderRequestStmt = $pdo->prepare($orderRequestSql);
        $orderRequestStmt->execute([
            'orderCollectionNumber' => $orderCollectionNumber,
            'customerID' => $customerID,
            'customerCode' => $customerCode,
            'stationNo' => $stationNo,
            'employeeID' => $employeeID,
            'salesTeamID' => $salesTeamID,
            'remarks' => $remarks
        ]);
        
        $orderRequestId = $pdo->lastInsertId();
        
        if (!$orderRequestId) {
            throw new Exception('Failed to create order request');
        }
        
        // Insert order items into OrderRequestDetails table (with updated required fields)
        $detailsSql = "
            INSERT INTO OrderRequestDetails (
                OrderRequestID,
                ProductID,
                `Product Code`,
                Quantity,
                UnitPrice,
                ExtendedPrice
            ) VALUES (
                :orderRequestId,
                :productId,
                :productCode,
                :quantity,
                :unitPrice,
                :extendedPrice
            )
        ";
        
        $detailsStmt = $pdo->prepare($detailsSql);
        $insertedDetails = [];
        
        foreach ($orderItems as $item) {
            // Validate item fields
            if (!isset($item['productCode']) || !isset($item['productID']) || !isset($item['quantity']) || !isset($item['unitPrice'])) {
                throw new Exception('Invalid order item: missing required fields');
            }

            $productID = (int)$item['productID'];
            $productCode = $item['productCode'];
            $quantity = (float)$item['quantity'];
            $unitPrice = (float)$item['unitPrice'];
            $discount = (float)($item['discount'] ?? 0); // Discount as percentage (0-1)

            // Calculate ExtendedPrice: UnitPrice * Quantity * (1 - Discount)
            $extendedPrice = $unitPrice * $quantity * (1 - $discount);

            $detailsStmt->execute([
                'orderRequestId' => $orderRequestId,
                'productId' => $productID,
                'productCode' => $productCode,
                'quantity' => $quantity,
                'unitPrice' => $unitPrice,
                'extendedPrice' => $extendedPrice
            ]);
            
            $detailId = $pdo->lastInsertId();
            
            $insertedDetails[] = [
                'OrderRequestDetailID' => (int)$detailId,
                'ProductID' => $productID,
                'ProductCode' => $productCode,
                'Quantity' => $quantity,
                'UnitPrice' => $unitPrice,
                'ExtendedPrice' => $extendedPrice,
                'Discount' => $discount
            ];
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Calculate total amount from extended prices
        $totalAmount = array_sum(array_column($insertedDetails, 'ExtendedPrice'));

        // Return success response
        $response = [
            'success' => true,
            'message' => 'Order created successfully',
            'data' => [
                'OrderRequestID' => (int)$orderRequestId,
                'OrderCollectionNumber' => $orderCollectionNumber,
                'CustomerID' => $customerID,
                'CustomerCode' => $customerCode,
                'EmployeeID' => $employeeID,
                'SalesTeamID' => $salesTeamID,
                'StationNo' => $stationNo,
                'TotalAmount' => $totalAmount,
                'OrderItems' => $insertedDetails,
                'ItemCount' => count($insertedDetails)
            ]
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error creating order: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
