package com.intelligent.evolvaordercollection.sync

import android.content.Context
import androidx.work.*
import com.intelligent.evolvaordercollection.data.repository.SyncRepository
import kotlinx.coroutines.flow.collect
import java.util.concurrent.TimeUnit

class SyncWorker(
    context: Context,
    params: WorkerParameters,
    private val syncRepository: SyncRepository
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result {
        return try {
            // Check if network is available
            if (!syncRepository.isNetworkAvailable()) {
                return Result.retry()
            }
            
            // Perform sync
            var syncSuccessful = true
            syncRepository.performFullSync().collect { progress ->
                if (progress.isError) {
                    syncSuccessful = false
                }
            }
            
            if (syncSuccessful) {
                // Update last sync time
                val sharedPrefs = applicationContext.getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
                sharedPrefs.edit().putLong("last_sync_time", System.currentTimeMillis()).apply()
                
                Result.success()
            } else {
                Result.retry()
            }
            
        } catch (e: Exception) {
            Result.failure()
        }
    }
    
    companion object {
        const val WORK_NAME = "pos_sync_work"
        
        fun schedulePeriodicSync(context: Context, intervalMinutes: Long = 15) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(true)
                .build()
            
            val syncRequest = PeriodicWorkRequestBuilder<SyncWorker>(intervalMinutes, TimeUnit.MINUTES)
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.LINEAR,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()
            
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                syncRequest
            )
        }
        
        fun scheduleOneTimeSync(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()
            
            val syncRequest = OneTimeWorkRequestBuilder<SyncWorker>()
                .setConstraints(constraints)
                .build()
            
            WorkManager.getInstance(context).enqueue(syncRequest)
        }
        
        fun cancelSync(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
        }
    }
}

// Sync Manager to handle sync operations
class SyncManager(
    private val context: Context,
    private val syncRepository: SyncRepository
) {
    
    fun startAutoSync(intervalMinutes: Long = 15) {
        SyncWorker.schedulePeriodicSync(context, intervalMinutes)
    }
    
    fun stopAutoSync() {
        SyncWorker.cancelSync(context)
    }
    
    fun triggerManualSync() {
        SyncWorker.scheduleOneTimeSync(context)
    }
    
    suspend fun getSyncStatus() = syncRepository.getSyncStatus()
    
    fun isAutoSyncEnabled(): Boolean {
        val sharedPrefs = context.getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        return sharedPrefs.getBoolean("auto_sync_enabled", true)
    }
    
    fun setAutoSyncEnabled(enabled: Boolean) {
        val sharedPrefs = context.getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        sharedPrefs.edit().putBoolean("auto_sync_enabled", enabled).apply()
        
        if (enabled) {
            startAutoSync()
        } else {
            stopAutoSync()
        }
    }
    
    fun setSyncInterval(intervalMinutes: Long) {
        val sharedPrefs = context.getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        sharedPrefs.edit().putLong("sync_interval_minutes", intervalMinutes).apply()
        
        if (isAutoSyncEnabled()) {
            stopAutoSync()
            startAutoSync(intervalMinutes)
        }
    }
    
    fun getSyncInterval(): Long {
        val sharedPrefs = context.getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        return sharedPrefs.getLong("sync_interval_minutes", 15)
    }
}
