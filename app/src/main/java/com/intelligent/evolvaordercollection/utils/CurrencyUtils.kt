package com.intelligent.evolvaordercollection.utils

import java.math.BigDecimal
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale

/**
 * Utility functions for formatting currency in Myanmar Kyat
 */
object CurrencyUtils {
    
    // Use English locale to ensure Arabic numerals (0-9) instead of Burmese numerals
    private val englishLocale = Locale.ENGLISH
    private val decimalFormat = DecimalFormat("#,##0", DecimalFormatSymbols(englishLocale))
    
    /**
     * Format a BigDecimal amount as Myanmar Kyat currency
     * @param amount The amount to format
     * @return Formatted string with "Ks" suffix (e.g., "2,500 Ks")
     */
    fun formatPrice(amount: BigDecimal): String {
        return "${decimalFormat.format(amount)} Ks"
    }
    
    /**
     * Format a Double amount as Myanmar Kyat currency
     * @param amount The amount to format
     * @return Formatted string with "Ks" suffix (e.g., "2,500 Ks")
     */
    fun formatPrice(amount: Double): String {
        return formatPrice(BigDecimal.valueOf(amount))
    }
    
    /**
     * Format an Int amount as Myanmar Kyat currency
     * @param amount The amount to format
     * @return Formatted string with "Ks" suffix (e.g., "2,500 Ks")
     */
    fun formatPrice(amount: Int): String {
        return formatPrice(BigDecimal.valueOf(amount.toLong()))
    }
    
    /**
     * Format a Long amount as Myanmar Kyat currency
     * @param amount The amount to format
     * @return Formatted string with "Ks" suffix (e.g., "2,500 Ks")
     */
    fun formatPrice(amount: Long): String {
        return formatPrice(BigDecimal.valueOf(amount))
    }
    
    /**
     * Format a String amount as Myanmar Kyat currency
     * @param amount The amount to format as string
     * @return Formatted string with "Ks" suffix (e.g., "2,500 Ks")
     */
    fun formatPrice(amount: String): String {
        return try {
            formatPrice(BigDecimal(amount))
        } catch (e: NumberFormatException) {
            "0 Ks"
        }
    }
}
