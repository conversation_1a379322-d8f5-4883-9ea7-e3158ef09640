package com.intelligent.evolvaordercollection.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Utility functions for responsive design across different screen sizes
 */
object ResponsiveUtils {
    
    // Screen size categories
    enum class ScreenSize {
        COMPACT,        // Phones (< 600dp width)
        MEDIUM,         // Small tablets (600dp - 839dp width)
        EXPANDED        // Large tablets (>= 840dp width)
    }
    
    // Device type categories
    enum class DeviceType {
        PHONE,
        SMALL_TABLET,   // 7-9 inch tablets
        LARGE_TABLET    // 10+ inch tablets
    }
    
    /**
     * Get current screen size category
     */
    @Composable
    fun getScreenSize(): ScreenSize {
        val configuration = LocalConfiguration.current
        val screenWidth = configuration.screenWidthDp
        
        return when {
            screenWidth < 600 -> ScreenSize.COMPACT
            screenWidth < 840 -> ScreenSize.MEDIUM
            else -> ScreenSize.EXPANDED
        }
    }
    
    /**
     * Get device type based on screen dimensions and density
     */
    @Composable
    fun getDeviceType(): DeviceType {
        val configuration = LocalConfiguration.current
        val density = LocalDensity.current
        
        val screenWidth = configuration.screenWidthDp
        val screenHeight = configuration.screenHeightDp
        val screenDiagonal = kotlin.math.sqrt(
            (screenWidth * screenWidth + screenHeight * screenHeight).toDouble()
        )
        
        return when {
            screenDiagonal < 600 -> DeviceType.PHONE
            screenDiagonal < 900 -> DeviceType.SMALL_TABLET
            else -> DeviceType.LARGE_TABLET
        }
    }
    
    /**
     * Check if current device is a compact screen (phone or small tablet)
     */
    @Composable
    fun isCompactScreen(): Boolean {
        val configuration = LocalConfiguration.current
        val screenWidth = configuration.screenWidthDp
        val screenHeight = configuration.screenHeightDp
        val screenDiagonal = kotlin.math.sqrt(
            (screenWidth * screenWidth + screenHeight * screenHeight).toDouble()
        )
        
        // Consider screens with diagonal < 10 inches as compact
        return screenDiagonal < 900 || screenWidth < 700
    }
    
    /**
     * Check if device is in landscape orientation
     */
    @Composable
    fun isLandscape(): Boolean {
        val configuration = LocalConfiguration.current
        return configuration.screenWidthDp > configuration.screenHeightDp
    }
    
    /**
     * Get responsive padding based on screen size
     */
    @Composable
    fun getResponsivePadding(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 8.dp
            ScreenSize.MEDIUM -> 12.dp
            ScreenSize.EXPANDED -> 16.dp
        }
    }
    
    /**
     * Get responsive spacing based on screen size
     */
    @Composable
    fun getResponsiveSpacing(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 6.dp
            ScreenSize.MEDIUM -> 8.dp
            ScreenSize.EXPANDED -> 12.dp
        }
    }
    
    /**
     * Get responsive card elevation based on screen size
     */
    @Composable
    fun getResponsiveElevation(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 2.dp
            ScreenSize.MEDIUM -> 4.dp
            ScreenSize.EXPANDED -> 6.dp
        }
    }
    
    /**
     * Get responsive corner radius based on screen size
     */
    @Composable
    fun getResponsiveCornerRadius(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 8.dp
            ScreenSize.MEDIUM -> 12.dp
            ScreenSize.EXPANDED -> 16.dp
        }
    }
    
    /**
     * Get responsive button height based on screen size
     */
    @Composable
    fun getResponsiveButtonHeight(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 36.dp
            ScreenSize.MEDIUM -> 40.dp
            ScreenSize.EXPANDED -> 48.dp
        }
    }
    
    /**
     * Get responsive icon size based on screen size
     */
    @Composable
    fun getResponsiveIconSize(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 16.dp
            ScreenSize.MEDIUM -> 20.dp
            ScreenSize.EXPANDED -> 24.dp
        }
    }
    
    /**
     * Get responsive minimum touch target size
     */
    @Composable
    fun getMinTouchTarget(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 40.dp
            ScreenSize.MEDIUM -> 44.dp
            ScreenSize.EXPANDED -> 48.dp
        }
    }
    
    /**
     * Get responsive grid item minimum size for product cards
     */
    @Composable
    fun getProductCardMinSize(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 85.dp   // Further reduced for more cards
            ScreenSize.MEDIUM -> 95.dp    // Further reduced for more cards
            ScreenSize.EXPANDED -> 110.dp // Further reduced for more cards
        }
    }
    
    /**
     * Check if we should use single pane layout
     */
    @Composable
    fun shouldUseSinglePane(): Boolean {
        val configuration = LocalConfiguration.current
        val screenWidth = configuration.screenWidthDp
        val isLandscape = isLandscape()
        
        // Use single pane for phones and small tablets in portrait
        return screenWidth < 700 && !isLandscape
    }
    
    /**
     * Get responsive navigation rail width
     */
    @Composable
    fun getNavigationRailWidth(): Dp {
        return when (getScreenSize()) {
            ScreenSize.COMPACT -> 56.dp
            ScreenSize.MEDIUM -> 64.dp
            ScreenSize.EXPANDED -> 72.dp
        }
    }
}
