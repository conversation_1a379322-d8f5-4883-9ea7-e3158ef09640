package com.intelligent.evolvaordercollection.config

/**
 * Global Application Configuration
 * Set StationID here before building APK
 */
object AppConfig {
    
    /**
     * Station ID - Set this value based on ClientID before building APK
     * This identifies which station/location this app instance represents
     */
    const val STATION_ID = 1 // TODO: Change this value based on ClientID before building APK
    
    /**
     * API Base URL
     */
    const val API_BASE_URL = "https://www.intelligentmyanmar.com/OrderCollectionApp(DemoROasis)/api/"
    
    /**
     * Order Collection Number Format
     * Format: CustomerCode_yyyyMMddhhmmss
     */
    const val ORDER_NUMBER_FORMAT = "%s_%s" // CustomerCode_timestamp
    
    /**
     * Default Walk-in Customer Code
     */
    const val WALK_IN_CUSTOMER_CODE = "WALKIN"
    
    /**
     * Order Status Constants
     */
    object OrderStatus {
        const val PENDING = "Pending"
        const val CONFIRMED = "Confirmed"
        const val PROCESSING = "Processing"
        const val COMPLETED = "Completed"
        const val CANCELLED = "Cancelled"
    }
    
    /**
     * App Version Info
     */
    const val APP_VERSION = "1.0.0"
    const val APP_NAME = "Order Collection App"
    
    /**
     * Database Configuration
     */
    object Database {
        const val NAME = "pos_database"
        const val VERSION = 5
    }
    
    /**
     * Network Configuration
     */
    object Network {
        const val TIMEOUT_SECONDS = 30L
        const val RETRY_ATTEMPTS = 3
    }
    
    /**
     * UI Configuration
     */
    object UI {
        const val ITEMS_PER_PAGE = 50
        const val SEARCH_DEBOUNCE_MS = 300L
    }
}
