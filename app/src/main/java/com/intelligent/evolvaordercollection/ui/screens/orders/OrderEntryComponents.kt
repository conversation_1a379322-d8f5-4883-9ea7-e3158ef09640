package com.intelligent.evolvaordercollection.ui.screens.orders

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.runtime.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.intelligent.evolvaordercollection.data.model.CartItem
import com.intelligent.evolvaordercollection.data.model.Customer
import com.intelligent.evolvaordercollection.utils.CurrencyUtils
import com.intelligent.evolvaordercollection.utils.ResponsiveUtils

@Composable
fun InvoiceItemCard(
    cartItem: CartItem,
    onQuantityChange: (Int) -> Unit,
    onRemove: () -> Unit,
    onDiscountChange: (Double) -> Unit = {},
    isCompact: Boolean = false
) {
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()
    val responsiveIconSize = ResponsiveUtils.getResponsiveIconSize()

    var showDiscountDialog by remember { mutableStateOf(false) }
    var showQuantityDialog by remember { mutableStateOf(false) }
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(if (isCompact) 2.dp else responsivePadding * 0.6f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Product Image
            AsyncImage(
                model = if (cartItem.product.imageUrl.isNotBlank()) {
                    cartItem.product.imageUrl
                } else {
                    getProductImageUrl(cartItem.product.category)
                },
                contentDescription = cartItem.product.name,
                modifier = Modifier
                    .size(if (isCompact) 28.dp else 40.dp)
                    .clip(RoundedCornerShape(if (isCompact) 2.dp else 4.dp)),
                contentScale = ContentScale.Crop
            )

            Spacer(modifier = Modifier.width(if (isCompact) 4.dp else responsiveSpacing * 0.7f))

            // Product Info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = cartItem.product.name,
                    style = if (isCompact) MaterialTheme.typography.bodySmall else MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${CurrencyUtils.formatPrice(cartItem.product.price)} × ${cartItem.quantity}",
                        style = if (isCompact) MaterialTheme.typography.labelSmall else MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    // Discount indicator
                    if (cartItem.discount > 0.0) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "(-${cartItem.discount}%)",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.error,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                Text(
                    text = CurrencyUtils.formatPrice(cartItem.totalPrice),
                    style = if (isCompact) MaterialTheme.typography.bodySmall else MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            // Compact Quantity and Discount Controls
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                // Decrease button
                IconButton(
                    onClick = {
                        if (cartItem.quantity > 1) {
                            onQuantityChange(cartItem.quantity - 1)
                        }
                    },
                    enabled = cartItem.quantity > 1,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Remove,
                        contentDescription = "Decrease quantity",
                        modifier = Modifier.size(12.dp)
                    )
                }

                // Clickable quantity field
                OutlinedButton(
                    onClick = { showQuantityDialog = true },
                    modifier = Modifier
                        .width(36.dp)
                        .height(24.dp),
                    contentPadding = PaddingValues(1.dp),
                    shape = RoundedCornerShape(4.dp)
                ) {
                    Text(
                        text = cartItem.quantity.toString(),
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold
                    )
                }

                // Increase button
                IconButton(
                    onClick = { onQuantityChange(cartItem.quantity + 1) },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Increase quantity",
                        modifier = Modifier.size(12.dp)
                    )
                }

                // Discount button
                OutlinedButton(
                    onClick = { showDiscountDialog = true },
                    modifier = Modifier
                        .width(32.dp)
                        .height(24.dp),
                    contentPadding = PaddingValues(1.dp),
                    shape = RoundedCornerShape(4.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = if (cartItem.discount > 0) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurfaceVariant
                    )
                ) {
                    Text(
                        text = if (cartItem.discount > 0) "${cartItem.discount.toInt()}%" else "%",
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold
                    )
                }

                // Delete button
                IconButton(
                    onClick = onRemove,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Remove item",
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(12.dp)
                    )
                }
            }
        }
    }

    // Quantity Edit Dialog
    if (showQuantityDialog) {
        QuantityEditDialog(
            currentQuantity = cartItem.quantity,
            onQuantityChange = { newQuantity ->
                onQuantityChange(newQuantity)
                showQuantityDialog = false
            },
            onDismiss = { showQuantityDialog = false }
        )
    }

    // Discount Edit Dialog
    if (showDiscountDialog) {
        DiscountEditDialog(
            currentDiscount = cartItem.discount,
            onDiscountChange = { newDiscount ->
                onDiscountChange(newDiscount)
                showDiscountDialog = false
            },
            onDismiss = { showDiscountDialog = false }
        )
    }
}

@Composable
fun CustomerSelectionDialog(
    customers: List<Customer>,
    onCustomerSelected: (Customer) -> Unit,
    onDismiss: () -> Unit
) {
    var searchQuery by remember { mutableStateOf("") }

    // Filter customers based on search query
    val filteredCustomers = remember(customers, searchQuery) {
        if (searchQuery.isBlank()) {
            customers
        } else {
            customers.filter { customer ->
                customer.name.contains(searchQuery, ignoreCase = true) ||
                customer.customerCode.contains(searchQuery, ignoreCase = true) ||
                customer.phone.contains(searchQuery, ignoreCase = true) ||
                customer.email.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Select Customer")
        },
        text = {
            Column {
                // Search TextField
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    label = { Text("Search customers...") },
                    leadingIcon = {
                        Icon(Icons.Default.Search, contentDescription = "Search")
                    },
                    trailingIcon = {
                        if (searchQuery.isNotEmpty()) {
                            IconButton(onClick = { searchQuery = "" }) {
                                Icon(Icons.Default.Clear, contentDescription = "Clear")
                            }
                        }
                    },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Customer List
                LazyColumn(
                    modifier = Modifier.height(300.dp)
                ) {
                    items(filteredCustomers) { customer ->
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            onClick = { onCustomerSelected(customer) }
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.Top
                                ) {
                                    Column(modifier = Modifier.weight(1f)) {
                                        Text(
                                            text = customer.name,
                                            style = MaterialTheme.typography.titleMedium,
                                            fontWeight = FontWeight.Bold
                                        )
                                        if (customer.customerCode.isNotBlank()) {
                                            Text(
                                                text = "Code: ${customer.customerCode}",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    }

                                    // Walk-in indicator
                                    if (customer.name.equals("Walk In Customer", ignoreCase = true)) {
                                        Surface(
                                            color = MaterialTheme.colorScheme.secondaryContainer,
                                            shape = RoundedCornerShape(8.dp)
                                        ) {
                                            Text(
                                                text = "Walk-in",
                                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                                                style = MaterialTheme.typography.labelSmall,
                                                color = MaterialTheme.colorScheme.onSecondaryContainer
                                            )
                                        }
                                    }
                                }

                                if (customer.phone.isNotBlank()) {
                                    Text(
                                        text = customer.phone,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                                if (customer.address.isNotBlank()) {
                                    Text(
                                        text = customer.address,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        maxLines = 2,
                                        overflow = TextOverflow.Ellipsis
                                    )
                                }
                            }
                        }
                    }

                    // Show message if no customers found
                    if (filteredCustomers.isEmpty() && searchQuery.isNotBlank()) {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(32.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "No customers found for \"$searchQuery\"",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun QuantityEditDialog(
    currentQuantity: Int,
    onQuantityChange: (Int) -> Unit,
    onDismiss: () -> Unit
) {
    var quantityText by remember { mutableStateOf(currentQuantity.toString()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Edit Quantity") },
        text = {
            OutlinedTextField(
                value = quantityText,
                onValueChange = { newValue ->
                    if (newValue.all { it.isDigit() } && newValue.length <= 3) {
                        quantityText = newValue
                    }
                },
                label = { Text("Quantity") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                singleLine = true,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val quantity = quantityText.toIntOrNull()
                    if (quantity != null && quantity > 0) {
                        onQuantityChange(quantity)
                    }
                }
            ) {
                Text("Save")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun DiscountEditDialog(
    currentDiscount: Double,
    onDiscountChange: (Double) -> Unit,
    onDismiss: () -> Unit
) {
    var discountText by remember { mutableStateOf(currentDiscount.toString()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Apply Discount") },
        text = {
            Column {
                OutlinedTextField(
                    value = discountText,
                    onValueChange = { newValue ->
                        if (newValue.matches(Regex("^\\d*\\.?\\d*$")) && newValue.length <= 5) {
                            discountText = newValue
                        }
                    },
                    label = { Text("Discount (%)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Enter discount percentage (0-100)",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val discount = discountText.toDoubleOrNull()
                    if (discount != null && discount >= 0 && discount <= 100) {
                        onDiscountChange(discount)
                    }
                }
            ) {
                Text("Apply")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

// Helper function for product images
fun getProductImageUrl(category: String): String {
    return when (category.lowercase()) {
        "rice & grains" -> "https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format"
        "cooking oil" -> "https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400&h=300&fit=crop&auto=format"
        "condiments" -> "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400&h=300&fit=crop&auto=format"
        "noodles" -> "https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop&auto=format"
        "canned goods" -> "https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=300&fit=crop&auto=format"
        "spices" -> "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&auto=format"
        "beverages" -> "https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop&auto=format"
        "snacks" -> "https://images.unsplash.com/photo-1621939514649-280e2ee25f60?w=400&h=300&fit=crop&auto=format"
        "nuts" -> "https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=300&fit=crop&auto=format"
        "dried fruits" -> "https://images.unsplash.com/photo-1577003833619-76bbd7f82948?w=400&h=300&fit=crop&auto=format"
        "seeds" -> "https://images.unsplash.com/photo-1509358271058-acd22cc93898?w=400&h=300&fit=crop&auto=format"
        "sweeteners" -> "https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400&h=300&fit=crop&auto=format"
        "seafood" -> "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop&auto=format"
        else -> "https://images.unsplash.com/photo-1542838132-92c53300491e?w=400&h=300&fit=crop&auto=format"
    }
}
