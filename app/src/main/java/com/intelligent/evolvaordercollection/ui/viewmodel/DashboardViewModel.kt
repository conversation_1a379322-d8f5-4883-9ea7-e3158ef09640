package com.intelligent.evolvaordercollection.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.intelligent.evolvaordercollection.data.repository.OrderRepository
import com.intelligent.evolvaordercollection.data.repository.ProductRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.math.BigDecimal

class DashboardViewModel(
    private val orderRepository: OrderRepository,
    private val productRepository: ProductRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()
    
    init {
        loadDashboardData()
    }
    
    private fun loadDashboardData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val todayOrderCount = orderRepository.getTodayOrderCount()
                val todayOrderTotal = orderRepository.getTodayOrderTotal()
                val totalProducts = productRepository.getTotalProductCount()

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    todayOrderCount = todayOrderCount,
                    todayOrderTotal = todayOrderTotal,
                    totalProducts = totalProducts
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }
    
    fun refreshData() {
        loadDashboardData()
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class DashboardUiState(
    val isLoading: Boolean = false,
    val todayOrderCount: Int = 0,
    val todayOrderTotal: BigDecimal = BigDecimal.ZERO,
    val totalProducts: Int = 0,
    val error: String? = null
)
