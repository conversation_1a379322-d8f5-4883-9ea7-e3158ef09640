package com.intelligent.evolvaordercollection.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.intelligent.evolvaordercollection.data.model.LoginRequest
import com.intelligent.evolvaordercollection.data.model.UserSession
import com.intelligent.evolvaordercollection.data.network.NetworkModule
import com.intelligent.evolvaordercollection.data.network.NetworkResult
import com.intelligent.evolvaordercollection.data.network.safeApiCall
import com.intelligent.evolvaordercollection.data.repository.UserSessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class LoginViewModel : ViewModel() {
    
    private val apiService = NetworkModule.provideProductApiService()
    private val sessionManager = UserSessionManager.getInstance()
    
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()
    
    init {
        // Check if user is already logged in
        checkExistingSession()
    }
    
    /**
     * Check if user has an existing valid session
     */
    private fun checkExistingSession() {
        val existingSession = sessionManager.getCurrentSession()
        if (existingSession != null && existingSession.isLoggedIn) {
            _uiState.value = _uiState.value.copy(
                isLoggedIn = true,
                userSession = existingSession,
                isLoading = false
            )
        }
    }
    
    /**
     * Perform user login
     */
    fun login(username: String, password: String) {
        if (username.isBlank() || password.isBlank()) {
            _uiState.value = _uiState.value.copy(
                error = "Username and password are required",
                isLoading = false
            )
            return
        }
        
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null
                )
                
                val loginRequest = LoginRequest(
                    username = username.trim(),
                    password = password.trim()
                )
                
                val result = safeApiCall { apiService.login(loginRequest) }
                
                when (result) {
                    is NetworkResult.Success -> {
                        if (result.data.success && result.data.data != null) {
                            val loginData = result.data.data
                            val userSession = UserSession(
                                user = loginData.user,
                                sessionToken = loginData.sessionToken,
                                loginTime = loginData.loginTime
                            )
                            
                            // Save session
                            sessionManager.saveSession(userSession)
                            
                            _uiState.value = _uiState.value.copy(
                                isLoggedIn = true,
                                userSession = userSession,
                                isLoading = false,
                                error = null
                            )
                        } else {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = result.data.message
                            )
                        }
                    }
                    is NetworkResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                    is NetworkResult.Loading -> {
                        // Already handled above
                    }
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Login failed: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Logout user
     */
    fun logout() {
        sessionManager.clearSession()
        _uiState.value = LoginUiState()
    }
    
    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * UI State for login screen
 */
data class LoginUiState(
    val isLoading: Boolean = false,
    val isLoggedIn: Boolean = false,
    val userSession: UserSession? = null,
    val error: String? = null
)
