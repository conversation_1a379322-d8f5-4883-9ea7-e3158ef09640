package com.intelligent.evolvaordercollection.ui.screens.orders

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import android.content.pm.ActivityInfo
import androidx.compose.ui.platform.LocalContext
import android.app.Activity
import android.content.Intent
import android.widget.Toast
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import coil.compose.AsyncImage
import com.intelligent.evolvaordercollection.POSApplication
import com.intelligent.evolvaordercollection.data.model.CartItem
import com.intelligent.evolvaordercollection.data.model.Customer
import com.intelligent.evolvaordercollection.data.model.Product
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderCollectionViewModel
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderCollectionUiState
import com.intelligent.evolvaordercollection.ui.viewmodel.ProductsViewModel
import com.intelligent.evolvaordercollection.ui.viewmodel.ProductsUiState
import com.intelligent.evolvaordercollection.ui.viewmodel.ViewModelFactory
import com.intelligent.evolvaordercollection.utils.CurrencyUtils
import com.intelligent.evolvaordercollection.utils.ResponsiveUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdvancedOrderEntryScreen(
    navController: NavController,
    orderViewModel: OrderCollectionViewModel = viewModel(
        factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
    ),
    productsViewModel: ProductsViewModel = viewModel(
        factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
    )
) {
    // Force landscape orientation for Order Entry
    val context = LocalContext.current
    LaunchedEffect(Unit) {
        val activity = context as? Activity
        activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
    }

    // Cleanup orientation when leaving this screen
    DisposableEffect(Unit) {
        onDispose {
            val activity = context as? Activity
            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }
    }

    val orderUiState by orderViewModel.uiState.collectAsState()
    val productsUiState by productsViewModel.uiState.collectAsState()
    val configuration = LocalConfiguration.current

    // Responsive layout calculations using ResponsiveUtils
    val screenWidth = configuration.screenWidthDp
    val screenHeight = configuration.screenHeightDp
    val isLandscape = ResponsiveUtils.isLandscape()
    val isCompact = ResponsiveUtils.isCompactScreen()
    val shouldUseSinglePane = ResponsiveUtils.shouldUseSinglePane()
    val screenSize = ResponsiveUtils.getScreenSize()

    // Adaptive pane weights based on screen size
    val (invoiceWeight, productsWeight) = when (screenSize) {
        ResponsiveUtils.ScreenSize.EXPANDED -> 0.35f to 0.65f
        ResponsiveUtils.ScreenSize.MEDIUM -> 0.4f to 0.6f
        ResponsiveUtils.ScreenSize.COMPACT -> if (isLandscape) 0.45f to 0.55f else 1f to 1f
    }



    // Background with subtle gradient
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    )
                )
            )
    ) {
        // Always use two-pane layout in landscape mode for Order Entry
        // This ensures selected products are always visible
        ResponsiveTwoPaneLayout(
            orderUiState = orderUiState,
            productsUiState = productsUiState,
            orderViewModel = orderViewModel,
            productsViewModel = productsViewModel,
            invoiceWeight = 0.4f, // Fixed weight to always show both panes
            productsWeight = 0.6f,
            navController = navController,
            isCompact = isCompact
        )
    }

    // Customer Selection Dialog
    if (orderUiState.showCustomerDialog) {
        CustomerSelectionDialog(
            customers = orderUiState.customers,
            onCustomerSelected = { customer ->
                orderViewModel.selectCustomer(customer)
            },
            onDismiss = { orderViewModel.hideCustomerDialog() }
        )
    }
}

@Composable
fun ResponsiveTwoPaneLayout(
    orderUiState: OrderCollectionUiState,
    productsUiState: ProductsUiState,
    orderViewModel: OrderCollectionViewModel,
    productsViewModel: ProductsViewModel,
    invoiceWeight: Float,
    productsWeight: Float,
    navController: NavController,
    isCompact: Boolean = false
) {
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()
    val responsiveCornerRadius = ResponsiveUtils.getResponsiveCornerRadius()
    val responsiveElevation = ResponsiveUtils.getResponsiveElevation()

    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(responsivePadding * 0.5f), // Reduced padding
        horizontalArrangement = Arrangement.spacedBy(responsiveSpacing * 0.5f) // Reduced spacing between panels
    ) {
        // Left Pane - Dynamic Invoice View
        Card(
            modifier = Modifier
                .weight(invoiceWeight)
                .fillMaxHeight(),
            elevation = CardDefaults.cardElevation(defaultElevation = responsiveElevation),
            shape = RoundedCornerShape(responsiveCornerRadius),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            DynamicInvoiceView(
                orderUiState = orderUiState,
                orderViewModel = orderViewModel,
                modifier = Modifier.fillMaxSize(),
                isCompact = isCompact || invoiceWeight < 0.4f
            )
        }

        // Right Pane - Interactive Product List View
        Card(
            modifier = Modifier
                .weight(productsWeight)
                .fillMaxHeight(),
            elevation = CardDefaults.cardElevation(defaultElevation = responsiveElevation),
            shape = RoundedCornerShape(responsiveCornerRadius),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            InteractiveProductListView(
                productsUiState = productsUiState,
                orderUiState = orderUiState,
                productsViewModel = productsViewModel,
                orderViewModel = orderViewModel,
                modifier = Modifier.fillMaxSize(),
                isCompact = isCompact
            )
        }
    }
}

@Composable
fun AdaptiveSinglePaneLayout(
    orderUiState: OrderCollectionUiState,
    productsUiState: ProductsUiState,
    orderViewModel: OrderCollectionViewModel,
    productsViewModel: ProductsViewModel,
    navController: NavController,
    isCompact: Boolean = false
) {
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()
    var currentPane by remember { mutableStateOf(OrderEntryPane.PRODUCTS) }
    val hasItems = orderUiState.cartItems.isNotEmpty()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(responsivePadding)
    ) {
        // Adaptive navigation bar
        AdaptiveNavigationBar(
            currentPane = currentPane,
            onPaneChange = { currentPane = it },
            hasItems = hasItems,
            itemCount = orderUiState.cartItems.size,
            isCompact = isCompact
        )

        Spacer(modifier = Modifier.height(responsiveSpacing))

        // Content based on selected pane
        AnimatedContent(
            targetState = currentPane,
            transitionSpec = {
                slideInHorizontally(
                    initialOffsetX = { if (targetState == OrderEntryPane.INVOICE) it else -it }
                ) + fadeIn() togetherWith slideOutHorizontally(
                    targetOffsetX = { if (targetState == OrderEntryPane.INVOICE) -it else it }
                ) + fadeOut()
            },
            label = "PaneTransition"
        ) { pane ->
            when (pane) {
                OrderEntryPane.PRODUCTS -> {
                    InteractiveProductListView(
                        productsUiState = productsUiState,
                        orderUiState = orderUiState,
                        productsViewModel = productsViewModel,
                        orderViewModel = orderViewModel,
                        modifier = Modifier.fillMaxSize(),
                        isCompact = isCompact
                    )
                }
                OrderEntryPane.INVOICE -> {
                    DynamicInvoiceView(
                        orderUiState = orderUiState,
                        orderViewModel = orderViewModel,
                        modifier = Modifier.fillMaxSize(),
                        isCompact = isCompact
                    )
                }
            }
        }
    }
}

@Composable
fun AdaptiveNavigationBar(
    currentPane: OrderEntryPane,
    onPaneChange: (OrderEntryPane) -> Unit,
    hasItems: Boolean,
    itemCount: Int,
    isCompact: Boolean = false
) {
    val responsiveCornerRadius = ResponsiveUtils.getResponsiveCornerRadius()
    val responsiveElevation = ResponsiveUtils.getResponsiveElevation()
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = responsiveElevation),
        shape = RoundedCornerShape(responsiveCornerRadius)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(responsivePadding / 2),
            horizontalArrangement = Arrangement.spacedBy(responsiveSpacing / 2)
        ) {
            // Products tab
            NavigationButton(
                text = "Products",
                icon = Icons.Default.Inventory,
                isSelected = currentPane == OrderEntryPane.PRODUCTS,
                onClick = { onPaneChange(OrderEntryPane.PRODUCTS) },
                modifier = Modifier.weight(1f)
            )

            // Invoice tab with badge
            Box(
                modifier = Modifier.weight(1f)
            ) {
                NavigationButton(
                    text = "Invoice",
                    icon = Icons.Default.Receipt,
                    isSelected = currentPane == OrderEntryPane.INVOICE,
                    onClick = { onPaneChange(OrderEntryPane.INVOICE) },
                    modifier = Modifier.fillMaxWidth()
                )

                // Item count badge
                if (hasItems) {
                    Badge(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .offset(x = (-8).dp, y = 8.dp)
                    ) {
                        Text(
                            text = itemCount.toString(),
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun NavigationButton(
    text: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        modifier = modifier,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primary
            else
                MaterialTheme.colorScheme.surfaceVariant,
            contentColor = if (isSelected)
                MaterialTheme.colorScheme.onPrimary
            else
                MaterialTheme.colorScheme.onSurfaceVariant
        ),
        shape = RoundedCornerShape(12.dp),
        contentPadding = PaddingValues(vertical = 12.dp, horizontal = 16.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(6.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.labelLarge,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium
        )
    }
}

enum class OrderEntryPane {
    PRODUCTS, INVOICE
}

@Composable
fun OrderSummarySection(
    orderUiState: OrderCollectionUiState,
    orderViewModel: OrderCollectionViewModel,
    isCompact: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF5F5F5) // Stable light gray background
        ),
        shape = RoundedCornerShape(if (isCompact) 8.dp else 12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(if (isCompact) 8.dp else 12.dp)
        ) {
            // Total (removed Subtotal as requested)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Total:",
                    style = if (isCompact) MaterialTheme.typography.titleMedium else MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333) // Stable dark gray color
                )
                Text(
                    text = CurrencyUtils.formatPrice(orderUiState.total),
                    style = if (isCompact) MaterialTheme.typography.titleMedium else MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1976D2) // Stable blue color for total amount
                )
            }
        }
    }
}



@Composable
fun EmptyCartState(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.ShoppingCart,
                contentDescription = null,
                modifier = Modifier.size(80.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Your cart is empty",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Start adding products to create an order",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun DynamicInvoiceView(
    orderUiState: OrderCollectionUiState,
    orderViewModel: OrderCollectionViewModel,
    modifier: Modifier = Modifier,
    isCompact: Boolean = false
) {
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()

    Column(
        modifier = modifier.padding(if (isCompact) 4.dp else responsivePadding * 0.4f) // Further reduced padding
    ) {
        // Customer Selection Section - moved to top
        CustomerSection(
            selectedCustomer = orderUiState.selectedCustomer,
            onSelectCustomer = { orderViewModel.showCustomerDialog() },
            isCompact = isCompact,
            remark = orderUiState.remark,
            onRemarkChange = { orderViewModel.updateRemark(it) }
        )

        Spacer(modifier = Modifier.height(if (isCompact) 2.dp else 4.dp))

        // Dynamic Cart Items with Real-time Updates
        if (orderUiState.cartItems.isNotEmpty()) {
            CartItemsSection(
                cartItems = orderUiState.cartItems,
                orderViewModel = orderViewModel,
                modifier = Modifier.weight(1f),
                isCompact = isCompact
            )

            Spacer(modifier = Modifier.height(if (isCompact) 6.dp else 10.dp))

            // Order Summary with Calculations
            OrderSummarySection(
                orderUiState = orderUiState,
                orderViewModel = orderViewModel,
                isCompact = isCompact
            )

            Spacer(modifier = Modifier.height(if (isCompact) 4.dp else 8.dp))

            // Action Buttons (without Save Draft)
            OrderActionButtons(
                orderUiState = orderUiState,
                orderViewModel = orderViewModel,
                isCompact = isCompact
            )
        } else {
            // Empty State
            EmptyCartState(
                modifier = Modifier.weight(1f)
            )
        }
    }
}



@Composable
fun NotesIconButton(
    remark: String,
    onRemarkChange: (String) -> Unit,
    isCompact: Boolean,
    showInline: Boolean = false
) {
    var showNotesDialog by remember { mutableStateOf(false) }

    val containerModifier = if (showInline) {
        Modifier
    } else {
        Modifier.fillMaxWidth()
    }

    val arrangement = if (showInline) {
        Arrangement.Center
    } else {
        Arrangement.End
    }

    Row(
        modifier = containerModifier,
        horizontalArrangement = arrangement
    ) {
        IconButton(
            onClick = { showNotesDialog = true },
            colors = IconButtonDefaults.iconButtonColors(
                containerColor = if (remark.isNotEmpty())
                    MaterialTheme.colorScheme.primaryContainer
                else
                    Color.Transparent,
                contentColor = if (remark.isNotEmpty())
                    MaterialTheme.colorScheme.onPrimaryContainer
                else
                    MaterialTheme.colorScheme.onSurfaceVariant
            ),
            modifier = Modifier.size(if (isCompact) 28.dp else 32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Description,
                contentDescription = "Add Notes",
                modifier = Modifier.size(if (isCompact) 14.dp else 16.dp)
            )
        }
    }

    // Notes Dialog
    if (showNotesDialog) {
        NotesDialog(
            remark = remark,
            onRemarkChange = onRemarkChange,
            onDismiss = { showNotesDialog = false }
        )
    }
}

@Composable
fun NotesDialog(
    remark: String,
    onRemarkChange: (String) -> Unit,
    onDismiss: () -> Unit
) {
    var tempRemark by remember { mutableStateOf(remark) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Order Notes")
        },
        text = {
            OutlinedTextField(
                value = tempRemark,
                onValueChange = { tempRemark = it },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("Add any special instructions or notes...") },
                maxLines = 4,
                shape = RoundedCornerShape(8.dp)
            )
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onRemarkChange(tempRemark)
                    onDismiss()
                }
            ) {
                Text("Save")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun CustomerSection(
    selectedCustomer: Customer?,
    onSelectCustomer: () -> Unit,
    isCompact: Boolean,
    remark: String = "",
    onRemarkChange: (String) -> Unit = {}
) {
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveCornerRadius = ResponsiveUtils.getResponsiveCornerRadius()

    if (selectedCustomer != null) {
        // Very compact customer display with notes icon at start
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.2f),
                    RoundedCornerShape(responsiveCornerRadius)
                )
                .padding(responsivePadding * 0.5f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Contact icon at the start
            Icon(
                imageVector = Icons.Default.Contacts,
                contentDescription = "Change Customer",
                tint = MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = 0.7f),
                modifier = Modifier
                    .size(if (isCompact) 12.dp else 14.dp)
                    .clickable { onSelectCustomer() }
            )

            Spacer(modifier = Modifier.width(4.dp))

            Column(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onSelectCustomer() }
            ) {
                Text(
                    text = selectedCustomer.name,
                    style = if (isCompact) MaterialTheme.typography.bodySmall else MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // Notes icon at the end
            NotesIconButton(
                remark = remark,
                onRemarkChange = onRemarkChange,
                isCompact = isCompact,
                showInline = true
            )
        }
        Spacer(modifier = Modifier.height(if (isCompact) 2.dp else 3.dp))
    } else {
        // Default customer display (Walk In Customer) with notes icon at start
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                    RoundedCornerShape(responsiveCornerRadius)
                )
                .padding(responsivePadding * 0.5f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Contact icon at the start
            Icon(
                imageVector = Icons.Default.Contacts,
                contentDescription = "Change Customer",
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                modifier = Modifier
                    .size(if (isCompact) 12.dp else 14.dp)
                    .clickable { onSelectCustomer() }
            )

            Spacer(modifier = Modifier.width(4.dp))

            Column(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onSelectCustomer() }
            ) {
                Text(
                    text = "Walk In Customer (Default)",
                    style = if (isCompact) MaterialTheme.typography.bodySmall else MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // Notes icon at the end
            NotesIconButton(
                remark = remark,
                onRemarkChange = onRemarkChange,
                isCompact = isCompact,
                showInline = true
            )
        }
        Spacer(modifier = Modifier.height(if (isCompact) 2.dp else 3.dp))
    }
}

@Composable
fun CartItemsSection(
    cartItems: List<CartItem>,
    orderViewModel: OrderCollectionViewModel,
    modifier: Modifier = Modifier,
    isCompact: Boolean
) {
    Column(modifier = modifier) {
        // Removed clear button to save space
        Spacer(modifier = Modifier.height(if (isCompact) 1.dp else 2.dp))

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(if (isCompact) 1.dp else 2.dp),
            contentPadding = PaddingValues(vertical = 1.dp)
        ) {
            items(cartItems, key = { it.product.id }) { cartItem ->
                AnimatedCartItem(
                    cartItem = cartItem,
                    onQuantityChange = { newQuantity ->
                        orderViewModel.updateCartItemQuantity(cartItem.product.id, newQuantity)
                    },
                    onRemove = {
                        orderViewModel.removeFromCart(cartItem.product.id)
                    },
                    onDiscountChange = { newDiscount ->
                        orderViewModel.updateCartItemDiscount(cartItem.product.id, newDiscount)
                    },
                    isCompact = isCompact
                )
            }
        }
    }
}

@Composable
fun AnimatedCartItem(
    cartItem: CartItem,
    onQuantityChange: (Int) -> Unit,
    onRemove: () -> Unit,
    onDiscountChange: (Double) -> Unit = {},
    isCompact: Boolean
) {
    var isExpanded by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                )
            ),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { isExpanded = !isExpanded }
                    .padding(if (isCompact) 6.dp else 10.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Product Image
                AsyncImage(
                    model = getProductImageUrl(cartItem.product.category),
                    contentDescription = cartItem.product.name,
                    modifier = Modifier
                        .size(if (isCompact) 40.dp else 50.dp)
                        .clip(RoundedCornerShape(6.dp)),
                    contentScale = ContentScale.Crop
                )

                Spacer(modifier = Modifier.width(if (isCompact) 6.dp else 8.dp))

                // Product Info - Compact two-line layout
                Column(modifier = Modifier.weight(1f)) {
                    // Line 1: Product Name
                    Text(
                        text = cartItem.product.name,
                        style = if (isCompact) MaterialTheme.typography.titleSmall else MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    // Line 2: Price x Qty (Dis%) and Amount on same line
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Left side: Price x Qty (Dis%)
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "${CurrencyUtils.formatPrice(cartItem.product.price)} × ${cartItem.quantity}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            // Discount indicator
                            if (cartItem.discount > 0.0) {
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "(-${cartItem.discount.toInt()}%)",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.error,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }

                        // Right side: Total Amount
                        Text(
                            text = CurrencyUtils.formatPrice(cartItem.totalPrice),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF1976D2) // Stable blue color for total amount
                        )
                    }
                }

                // Expand/Collapse indicator
                Icon(
                    imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = if (isExpanded) "Collapse" else "Expand",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // Expanded content with quantity controls
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically() + fadeIn(),
                exit = shrinkVertically() + fadeOut()
            ) {
                QuantityControlSection(
                    quantity = cartItem.quantity,
                    discount = cartItem.discount,
                    onQuantityChange = onQuantityChange,
                    onDiscountChange = onDiscountChange,
                    onRemove = onRemove,
                    isCompact = isCompact
                )
            }
        }
    }
}

@Composable
fun QuantityControlSection(
    quantity: Int,
    discount: Double,
    onQuantityChange: (Int) -> Unit,
    onDiscountChange: (Double) -> Unit,
    onRemove: () -> Unit,
    isCompact: Boolean
) {
    var showQuantityDialog by remember { mutableStateOf(false) }
    var showDiscountDialog by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = if (isCompact) 6.dp else 10.dp, vertical = if (isCompact) 4.dp else 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        ),
        shape = RoundedCornerShape(6.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(if (isCompact) 6.dp else 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Quantity",
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium
            )

            // Reorganized layout: Quantity (left), Discount (center), Delete (right)
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Left: Quantity controls
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // Decrease button
                    IconButton(
                        onClick = {
                            if (quantity > 1) onQuantityChange(quantity - 1)
                        },
                        enabled = quantity > 1,
                        modifier = Modifier.size(28.dp),
                        colors = IconButtonDefaults.iconButtonColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer,
                            contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Remove,
                            contentDescription = "Decrease quantity",
                            modifier = Modifier.size(14.dp)
                        )
                    }

                    // Clickable quantity display
                    OutlinedButton(
                        onClick = { showQuantityDialog = true },
                        modifier = Modifier
                            .width(40.dp)
                            .height(28.dp),
                        contentPadding = PaddingValues(2.dp),
                        shape = RoundedCornerShape(4.dp)
                    ) {
                        Text(
                            text = quantity.toString(),
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    // Increase button
                    IconButton(
                        onClick = { onQuantityChange(quantity + 1) },
                        modifier = Modifier.size(28.dp),
                        colors = IconButtonDefaults.iconButtonColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer,
                            contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Increase quantity",
                            modifier = Modifier.size(14.dp)
                        )
                    }
                }

                // Center: Discount controls
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(2.dp)
                ) {
                    Text(
                        text = "Dis %",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium
                    )

                    OutlinedButton(
                        onClick = { showDiscountDialog = true },
                        modifier = Modifier
                            .width(36.dp)
                            .height(28.dp),
                        contentPadding = PaddingValues(2.dp),
                        shape = RoundedCornerShape(4.dp),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = if (discount > 0) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    ) {
                        Text(
                            text = if (discount > 0) "${discount.toInt()}%" else "%",
                            style = MaterialTheme.typography.labelSmall,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                // Right: Remove button
                IconButton(
                    onClick = onRemove,
                    modifier = Modifier.size(28.dp),
                    colors = IconButtonDefaults.iconButtonColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer,
                        contentColor = MaterialTheme.colorScheme.onErrorContainer
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Remove item",
                        modifier = Modifier.size(14.dp)
                    )
                }
            }
        }
    }

    // Quantity Dialog
    if (showQuantityDialog) {
        QuantityInputDialog(
            currentQuantity = quantity,
            onQuantityChange = { newQuantity ->
                onQuantityChange(newQuantity)
                showQuantityDialog = false
            },
            onDismiss = { showQuantityDialog = false }
        )
    }

    // Discount Dialog
    if (showDiscountDialog) {
        DiscountInputDialog(
            currentDiscount = discount,
            onDiscountChange = { newDiscount ->
                onDiscountChange(newDiscount)
                showDiscountDialog = false
            },
            onDismiss = { showDiscountDialog = false }
        )
    }
}
@Composable
fun OrderActionButtons(
    orderUiState: OrderCollectionUiState,
    orderViewModel: OrderCollectionViewModel,
    isCompact: Boolean
) {
    val context = LocalContext.current

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Cancel Button
        OutlinedButton(
            onClick = { orderViewModel.clearCart() },
            modifier = Modifier.weight(1f),
            shape = RoundedCornerShape(8.dp),
            contentPadding = PaddingValues(vertical = if (isCompact) 8.dp else 12.dp),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.error
            ),
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.error)
        ) {
            Icon(
                imageVector = Icons.Default.Clear,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "Cancel",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium
            )
        }

        // Create Order Button
        Button(
            onClick = { orderViewModel.createOrder() },
            modifier = Modifier.weight(1f),
            enabled = orderUiState.selectedCustomer != null && orderUiState.cartItems.isNotEmpty(),
            shape = RoundedCornerShape(8.dp),
            contentPadding = PaddingValues(
                vertical = if (isCompact) 8.dp else 12.dp,
                horizontal = if (isCompact) 12.dp else 16.dp
            ),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.onPrimary
            )
        ) {
            Icon(
                imageVector = Icons.Default.ShoppingCart,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "Create Order",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
        }

        // Calculator Button
        IconButton(
            onClick = {
                // Try multiple methods to open calculator
                val calculatorPackages = listOf(
                    "com.android.calculator2",
                    "com.google.android.calculator",
                    "com.samsung.android.calculator",
                    "com.miui.calculator",
                    "com.oneplus.calculator",
                    "com.huawei.calculator",
                    "com.coloros.calculator",
                    "com.oppo.calculator",
                    "com.honor.calculator",
                    "com.hihonor.calculator",
                    "com.huawei.android.calculator",
                    "com.honor.android.calculator",
                    "com.hihonor.android.calculator",
                    "calculator",
                    "com.sec.android.app.popupcalculator",
                    "com.android.calculator",
                    "com.calculator",
                    "calculator.android",
                    "com.magicOS.calculator"
                )

                var launched = false

                // Method 1: Try category-based intent
                try {
                    val intent = Intent(Intent.ACTION_MAIN).apply {
                        addCategory(Intent.CATEGORY_APP_CALCULATOR)
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                    context.startActivity(intent)
                    launched = true
                } catch (e: Exception) {
                    // Continue to next method
                }

                // Method 2: Try specific calculator packages
                if (!launched) {
                    for (packageName in calculatorPackages) {
                        try {
                            val intent = context.packageManager.getLaunchIntentForPackage(packageName)
                            if (intent != null) {
                                context.startActivity(intent)
                                launched = true
                                break
                            }
                        } catch (e: Exception) {
                            // Continue to next package
                        }
                    }
                }

                // Method 3: Try to find any app with "calculator" in the name or package
                if (!launched) {
                    try {
                        val pm = context.packageManager
                        val apps = pm.getInstalledApplications(0)
                        val foundCalculators = mutableListOf<String>()

                        for (app in apps) {
                            val appName = pm.getApplicationLabel(app).toString().lowercase()
                            val packageName = app.packageName.lowercase()

                            if (appName.contains("calculator") || appName.contains("calc") ||
                                packageName.contains("calculator") || packageName.contains("calc")) {
                                foundCalculators.add("${app.packageName} - $appName")
                                val intent = pm.getLaunchIntentForPackage(app.packageName)
                                if (intent != null) {
                                    context.startActivity(intent)
                                    launched = true
                                    break
                                }
                            }
                        }

                        // Debug: Show found calculators if none launched
                        if (!launched && foundCalculators.isNotEmpty()) {
                            Toast.makeText(context, "Found calculators but couldn't launch: ${foundCalculators.joinToString(", ")}", Toast.LENGTH_LONG).show()
                        }
                    } catch (e: Exception) {
                        Toast.makeText(context, "Error searching for calculator: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }

                // Method 4: Try universal calculator intent
                if (!launched) {
                    try {
                        val intent = Intent("android.intent.action.CALCULATOR")
                        context.startActivity(intent)
                        launched = true
                    } catch (e: Exception) {
                        // Continue
                    }
                }

                // If still not launched, show a message
                if (!launched) {
                    Toast.makeText(context, "Calculator app not found on this device", Toast.LENGTH_SHORT).show()
                }
            },
            modifier = Modifier
                .size(if (isCompact) 40.dp else 48.dp)
                .background(
                    MaterialTheme.colorScheme.secondaryContainer,
                    RoundedCornerShape(8.dp)
                )
        ) {
            Icon(
                imageVector = Icons.Default.Calculate,
                contentDescription = "Calculator",
                tint = MaterialTheme.colorScheme.onSecondaryContainer,
                modifier = Modifier.size(if (isCompact) 18.dp else 20.dp)
            )
        }
    }
}







@Composable
fun OrderEntryDialogs(
    orderUiState: OrderCollectionUiState,
    orderViewModel: OrderCollectionViewModel
) {
    // Customer Selection Dialog
    if (orderUiState.showCustomerDialog) {
        CustomerSelectionDialog(
            customers = orderUiState.customers,
            onCustomerSelected = { customer ->
                orderViewModel.selectCustomer(customer)
            },
            onDismiss = { orderViewModel.hideCustomerDialog() }
        )
    }

    // Success/Error Snackbars could be added here
    // Order completion confirmation dialogs
    // Validation error dialogs
}

@Composable
fun QuantityInputDialog(
    currentQuantity: Int,
    onQuantityChange: (Int) -> Unit,
    onDismiss: () -> Unit
) {
    var tempQuantity by remember { mutableStateOf(currentQuantity.toString()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Enter Quantity") },
        text = {
            OutlinedTextField(
                value = tempQuantity,
                onValueChange = { tempQuantity = it },
                label = { Text("Quantity") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                singleLine = true,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val quantity = tempQuantity.toIntOrNull()
                    if (quantity != null && quantity > 0) {
                        onQuantityChange(quantity)
                    }
                }
            ) {
                Text("OK")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun DiscountInputDialog(
    currentDiscount: Double,
    onDiscountChange: (Double) -> Unit,
    onDismiss: () -> Unit
) {
    var tempDiscount by remember {
        mutableStateOf(
            if (currentDiscount > 0) currentDiscount.toInt().toString() else ""
        )
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Enter Discount %") },
        text = {
            OutlinedTextField(
                value = tempDiscount,
                onValueChange = { tempDiscount = it },
                label = { Text("Discount (%)") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                singleLine = true,
                modifier = Modifier.fillMaxWidth(),
                suffix = { Text("%") }
            )
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val discount = tempDiscount.toDoubleOrNull() ?: 0.0
                    if (discount >= 0 && discount <= 100) {
                        onDiscountChange(discount)
                    }
                }
            ) {
                Text("OK")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}


