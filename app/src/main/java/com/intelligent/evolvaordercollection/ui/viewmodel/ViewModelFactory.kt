package com.intelligent.evolvaordercollection.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.intelligent.evolvaordercollection.di.AppContainer

/**
 * ViewModelFactory for manual dependency injection.
 * This replaces <PERSON>lt's automatic ViewModel injection.
 */
class ViewModelFactory(private val appContainer: AppContainer) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when (modelClass) {
            ProductsViewModel::class.java -> {
                ProductsViewModel(appContainer.productRepository) as T
            }
            DashboardViewModel::class.java -> {
                DashboardViewModel(
                    appContainer.orderRepository,
                    appContainer.productRepository
                ) as T
            }
            OrderCollectionViewModel::class.java -> {
                OrderCollectionViewModel(
                    appContainer.orderRepository,
                    appContainer.customerRepository,
                    appContainer.productRepository
                ) as T
            }
            OrderManagementViewModel::class.java -> {
                OrderManagementViewModel(
                    appContainer.orderRepository
                ) as T
            }
            CustomersViewModel::class.java -> {
                CustomersViewModel(
                    appContainer.customerRepository
                ) as T
            }
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
}
