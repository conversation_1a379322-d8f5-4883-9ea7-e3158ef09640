package com.intelligent.evolvaordercollection.ui.screens.orders

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.intelligent.evolvaordercollection.POSApplication
import com.intelligent.evolvaordercollection.data.model.Order
import com.intelligent.evolvaordercollection.data.model.OrderStatus
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderManagementViewModel
import com.intelligent.evolvaordercollection.ui.viewmodel.ViewModelFactory
import com.intelligent.evolvaordercollection.utils.CurrencyUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrdersScreen(
    navController: NavController,
    viewModel: OrderManagementViewModel = viewModel(
        factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
    )
) {
    val uiState by viewModel.uiState.collectAsState()
    var selectedStatus by remember { mutableStateOf<OrderStatus?>(null) }

    // Load data when screen is first displayed
    LaunchedEffect(Unit) {
        viewModel.loadDashboardData()
    }

    // Filter orders based on selected status
    val filteredOrders = remember(uiState.orders, selectedStatus) {
        if (selectedStatus == null) {
            uiState.orders
        } else {
            uiState.orders.filter { it.status == selectedStatus }
        }
    }
    
    if (uiState.isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    } else {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Orders (${filteredOrders.size})",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )

                FloatingActionButton(
                    onClick = { navController.navigate("order_entry") },
                    modifier = Modifier.size(56.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Create Order"
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Status Filter
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                item {
                    FilterChip(
                        onClick = { selectedStatus = null },
                        label = { Text("All") },
                        selected = selectedStatus == null
                    )
                }
                items(OrderStatus.values()) { status ->
                    FilterChip(
                        onClick = { selectedStatus = status },
                        label = { Text(status.name.replace("_", " ")) },
                        selected = selectedStatus == status
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Orders List
            if (filteredOrders.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Receipt,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "No orders found",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Create your first order to get started",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                        )
                    }
                }
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(filteredOrders) { order ->
                        OrderCard(order = order)
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderCard(order: Order) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = order.orderNumber,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Customer: ${order.customerName}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "Total: ${CurrencyUtils.formatPrice(order.totalAmount)}",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                AssistChip(
                    onClick = { },
                    label = { Text(order.status.name) },
                    colors = AssistChipDefaults.assistChipColors(
                        containerColor = when (order.status) {
                            OrderStatus.WAITING -> MaterialTheme.colorScheme.secondaryContainer
                            OrderStatus.SUCCESS -> MaterialTheme.colorScheme.primaryContainer
                            OrderStatus.REJECT -> MaterialTheme.colorScheme.errorContainer
                        }
                    )
                )
            }
        }
    }
}
