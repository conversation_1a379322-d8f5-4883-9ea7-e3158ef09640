package com.intelligent.evolvaordercollection.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.intelligent.evolvaordercollection.data.model.*
import com.intelligent.evolvaordercollection.data.repository.CustomerRepository
import com.intelligent.evolvaordercollection.data.repository.OrderRepository
import com.intelligent.evolvaordercollection.data.repository.ProductRepository
import com.intelligent.evolvaordercollection.data.repository.RemoteCustomerRepository
import com.intelligent.evolvaordercollection.data.network.NetworkResult
import com.intelligent.evolvaordercollection.data.network.NetworkModule
import com.intelligent.evolvaordercollection.data.network.safeApiCall
import com.intelligent.evolvaordercollection.config.AppConfig
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.SimpleDateFormat
import java.util.*

class OrderCollectionViewModel(
    private val orderRepository: OrderRepository,
    private val customerRepository: CustomerRepository,
    private val productRepository: ProductRepository
) : ViewModel() {

    private val remoteCustomerRepository = RemoteCustomerRepository()
    private val apiService = NetworkModule.provideProductApiService()

    private val _uiState = MutableStateFlow(OrderCollectionUiState())
    val uiState: StateFlow<OrderCollectionUiState> = _uiState.asStateFlow()

    init {
        loadCustomers()
    }

    private fun loadCustomers() {
        viewModelScope.launch {
            try {
                val customers = customerRepository.getAllCustomersList()
                // Set "Walk In Customer" as default if available
                val defaultCustomer = customers.find { it.name == "Walk In Customer" }
                _uiState.value = _uiState.value.copy(
                    customers = customers,
                    selectedCustomer = defaultCustomer
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = e.message)
            }
        }
    }

    fun addToCart(product: Product, quantity: Int = 1) {
        val currentItems = _uiState.value.cartItems.toMutableList()
        val existingItemIndex = currentItems.indexOfFirst { it.product.id == product.id }

        if (existingItemIndex >= 0) {
            // Update existing item (preserve discount)
            val existingItem = currentItems[existingItemIndex]
            val newQuantity = existingItem.quantity + quantity
            currentItems[existingItemIndex] = existingItem.copy(quantity = newQuantity)
        } else {
            // Add new item
            currentItems.add(
                CartItem(
                    product = product,
                    quantity = quantity,
                    discount = 0.0
                )
            )
        }

        updateCartItems(currentItems)
    }

    fun removeFromCart(productId: Long) {
        val currentItems = _uiState.value.cartItems.toMutableList()
        currentItems.removeAll { it.product.id == productId }
        updateCartItems(currentItems)
    }

    fun updateCartItemQuantity(productId: Long, newQuantity: Int) {
        if (newQuantity <= 0) {
            removeFromCart(productId)
            return
        }

        val currentItems = _uiState.value.cartItems.toMutableList()
        val itemIndex = currentItems.indexOfFirst { it.product.id == productId }

        if (itemIndex >= 0) {
            val item = currentItems[itemIndex]
            currentItems[itemIndex] = item.copy(quantity = newQuantity)
            updateCartItems(currentItems)
        }
    }

    fun updateCartItemDiscount(productId: Long, discountPercent: Double) {
        val currentItems = _uiState.value.cartItems.toMutableList()
        val itemIndex = currentItems.indexOfFirst { it.product.id == productId }

        if (itemIndex >= 0) {
            val item = currentItems[itemIndex]
            currentItems[itemIndex] = item.copy(discount = discountPercent)
            updateCartItems(currentItems)
        }
    }

    fun clearCart() {
        // Reset to default "Walk In Customer" when clearing cart
        val defaultCustomer = _uiState.value.customers.find { it.name == "Walk In Customer" }
        updateCartItems(emptyList())
        _uiState.value = _uiState.value.copy(
            remark = "",
            selectedCustomer = defaultCustomer
        )
    }

    private fun updateCartItems(items: List<CartItem>) {
        val subtotal = items.sumOf { it.totalPrice }
        val discount = _uiState.value.discount
        val total = subtotal.subtract(discount)

        _uiState.value = _uiState.value.copy(
            cartItems = items,
            subtotal = subtotal,
            total = total
        )
    }

    fun selectCustomer(customer: Customer) {
        _uiState.value = _uiState.value.copy(
            selectedCustomer = customer,
            showCustomerDialog = false
        )
    }

    fun showCustomerDialog() {
        _uiState.value = _uiState.value.copy(showCustomerDialog = true)
    }

    fun hideCustomerDialog() {
        _uiState.value = _uiState.value.copy(showCustomerDialog = false)
    }

    fun applyDiscount(discountAmount: BigDecimal) {
        val subtotal = _uiState.value.subtotal
        val total = subtotal.subtract(discountAmount)

        _uiState.value = _uiState.value.copy(
            discount = discountAmount,
            total = total
        )
    }

    fun updateRemark(remark: String) {
        _uiState.value = _uiState.value.copy(remark = remark)
    }

    fun createOrder() {
        val currentState = _uiState.value
        if (currentState.cartItems.isEmpty()) {
            _uiState.value = currentState.copy(error = "Please add items to cart")
            return
        }

        viewModelScope.launch {
            try {
                _uiState.value = currentState.copy(isLoading = true, error = null)

                // Get customer code (use Walk-in if no customer selected)
                val customerCode = currentState.selectedCustomer?.customerCode
                    ?: AppConfig.WALK_IN_CUSTOMER_CODE

                // Calculate total amount
                val totalAmount = currentState.cartItems.sumOf {
                    it.quantity.toDouble() * it.product.price.toDouble()
                }

                // Create order items for API
                val orderItems = currentState.cartItems.map { cartItem ->
                    OrderItemRequest(
                        productCode = cartItem.product.productCode,
                        productName = cartItem.product.name,
                        quantity = cartItem.quantity.toDouble(),
                        unitPrice = cartItem.product.price.toDouble()
                    )
                }

                // Create order request
                val orderRequest = CreateOrderRequest(
                    customerCode = customerCode,
                    stationId = AppConfig.STATION_ID,
                    orderItems = orderItems,
                    totalAmount = totalAmount,
                    remarks = currentState.remark
                )

                // Call API to create order
                val result = safeApiCall { apiService.createOrder(orderRequest) }

                when (result) {
                    is NetworkResult.Success -> {
                        if (result.data.success) {
                            // Also save to local database for offline access
                            saveOrderLocally(currentState, result.data.data?.orderCollectionNumber ?: "")

                            // Clear cart and show success
                            val defaultCustomer = currentState.customers.find { it.name == "Walk In Customer" }
                            _uiState.value = OrderCollectionUiState(
                                customers = currentState.customers,
                                selectedCustomer = defaultCustomer,
                                isLoading = false,
                                orderCreated = true,
                                message = "✅ Order created successfully!\nOrder Number: ${result.data.data?.orderCollectionNumber}"
                            )
                        } else {
                            _uiState.value = currentState.copy(
                                isLoading = false,
                                error = "❌ Failed to create order: ${result.data.message}"
                            )
                        }
                    }
                    is NetworkResult.Error -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            error = "❌ Network error: ${result.message}"
                        )
                    }
                    is NetworkResult.Loading -> {
                        // Already handled above
                    }
                }

            } catch (e: Exception) {
                _uiState.value = currentState.copy(
                    isLoading = false,
                    error = "❌ Failed to create order: ${e.message}"
                )
            }
        }
    }

    /**
     * Save order to local database for offline access
     */
    private suspend fun saveOrderLocally(currentState: OrderCollectionUiState, orderNumber: String) {
        try {
            // Create local order object
            val order = Order(
                orderNumber = orderNumber,
                customerId = currentState.selectedCustomer?.id ?: 0L, // 0 for Walk-in
                customerName = currentState.selectedCustomer?.name ?: "Walk In Customer",
                status = OrderStatus.WAITING,
                subtotal = currentState.subtotal,
                taxAmount = BigDecimal.ZERO,
                discountAmount = currentState.discount,
                totalAmount = currentState.total
            )

            // Insert order
            val orderId = orderRepository.insertOrder(order)

            // Insert order items
            val orderItems = currentState.cartItems.map { cartItem ->
                OrderItem(
                    orderId = orderId,
                    productId = cartItem.product.id,
                    productName = cartItem.product.name,
                    quantity = cartItem.quantity,
                    unitPrice = cartItem.product.price,
                    totalPrice = cartItem.totalPrice
                )
            }

            orderRepository.insertOrderItems(orderItems)

        } catch (e: Exception) {
            println("Failed to save order locally: ${e.message}")
            // Don't fail the main operation if local save fails
        }
    }

    fun saveAsDraft() {
        val currentState = _uiState.value
        if (currentState.cartItems.isEmpty()) {
            _uiState.value = currentState.copy(error = "Cart is empty")
            return
        }

        viewModelScope.launch {
            try {
                _uiState.value = currentState.copy(isLoading = true)
                
                // Create draft order (using WAITING status since DRAFT no longer exists)
                val order = Order(
                    orderNumber = "DRAFT-${System.currentTimeMillis()}",
                    customerId = currentState.selectedCustomer?.id ?: 0,
                    customerName = currentState.selectedCustomer?.name ?: "Walk-in Customer",
                    status = OrderStatus.WAITING,
                    subtotal = currentState.subtotal,
                    taxAmount = BigDecimal.ZERO,
                    discountAmount = currentState.discount,
                    totalAmount = currentState.total
                )
                
                val orderId = orderRepository.insertOrder(order)
                
                // Create order items
                val orderItems = currentState.cartItems.map { cartItem ->
                    OrderItem(
                        orderId = orderId,
                        productId = cartItem.product.id,
                        productName = cartItem.product.name,
                        quantity = cartItem.quantity,
                        unitPrice = cartItem.product.price,
                        totalPrice = cartItem.totalPrice
                    )
                }
                
                orderRepository.insertOrderItems(orderItems)
                
                // Clear cart
                _uiState.value = OrderCollectionUiState(
                    customers = currentState.customers,
                    isLoading = false,
                    draftSaved = true
                )
                
            } catch (e: Exception) {
                _uiState.value = currentState.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }

    /**
     * Sync customers from remote database
     */
    fun syncCustomers() {
        viewModelScope.launch {
            try {
                println("Starting customer sync...")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                remoteCustomerRepository.getCustomers(
                    activeOnly = true,
                    limit = 10000 // Get all customers
                ).collect { result ->
                    when (result) {
                        is NetworkResult.Loading -> {
                            println("Loading remote customers...")
                        }
                        is NetworkResult.Success -> {
                            println("Successfully loaded ${result.data.size} remote customers")

                            // Convert remote customers to local customers and sync
                            val localCustomers = result.data.map { it.toLocalCustomer() }

                            var insertedCount = 0
                            var updatedCount = 0

                            localCustomers.forEach { customer ->
                                try {
                                    // Check if customer already exists by customer code
                                    val existingCustomer = customerRepository.getCustomerByCode(customer.customerCode)

                                    if (existingCustomer != null) {
                                        // Update existing customer
                                        val updatedCustomer = customer.copy(
                                            id = existingCustomer.id,
                                            createdAt = existingCustomer.createdAt,
                                            updatedAt = System.currentTimeMillis()
                                        )
                                        customerRepository.updateCustomer(updatedCustomer)
                                        updatedCount++
                                        println("Updated customer: ${customer.name}")
                                    } else {
                                        // Insert new customer
                                        customerRepository.insertCustomer(customer)
                                        insertedCount++
                                        println("Inserted new customer: ${customer.name}")
                                    }
                                } catch (e: Exception) {
                                    println("Failed to sync customer ${customer.name}: ${e.message}")
                                }
                            }

                            val totalSynced = insertedCount + updatedCount
                            println("Customer sync completed: $insertedCount new, $updatedCount updated")

                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = if (totalSynced > 0) {
                                    "✅ Customers synced: $insertedCount new, $updatedCount updated"
                                } else {
                                    "✅ All customers are up to date"
                                }
                            )

                            // Reload customers after sync
                            if (totalSynced > 0) {
                                loadCustomers()
                            }
                        }
                        is NetworkResult.Error -> {
                            println("Error loading remote customers: ${result.message}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = "❌ Failed to sync customers: ${result.message}"
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                println("Exception in syncCustomers: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "❌ Customer sync failed: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun resetOrderCreated() {
        _uiState.value = _uiState.value.copy(orderCreated = false)
    }

    fun resetDraftSaved() {
        _uiState.value = _uiState.value.copy(draftSaved = false)
    }

    fun updateSearchQuery(query: String) {
        _uiState.value = _uiState.value.copy(searchQuery = query)
    }

    fun getCartQuantity(productId: Long): Int {
        return _uiState.value.cartItems.find { it.product.id == productId }?.quantity ?: 0
    }
}

data class OrderCollectionUiState(
    val cartItems: List<CartItem> = emptyList(),
    val selectedCustomer: Customer? = null,
    val customers: List<Customer> = emptyList(),
    val subtotal: BigDecimal = BigDecimal.ZERO,
    val discount: BigDecimal = BigDecimal.ZERO,
    val total: BigDecimal = BigDecimal.ZERO,
    val remark: String = "",
    val isLoading: Boolean = false,
    val showCustomerDialog: Boolean = false,
    val orderCreated: Boolean = false,
    val draftSaved: Boolean = false,
    val searchQuery: String = "",
    val error: String? = null
)
