package com.intelligent.evolvaordercollection.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.intelligent.evolvaordercollection.data.model.Product
import com.intelligent.evolvaordercollection.data.model.RemoteProduct
import com.intelligent.evolvaordercollection.data.network.NetworkResult
import com.intelligent.evolvaordercollection.data.repository.ProductRepository
import com.intelligent.evolvaordercollection.data.repository.RemoteProductRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.math.BigDecimal

class ProductsViewModel(
    private val productRepository: ProductRepository
) : ViewModel() {

    private val remoteProductRepository = RemoteProductRepository()
    
    private val _uiState = MutableStateFlow(ProductsUiState())
    val uiState: StateFlow<ProductsUiState> = _uiState.asStateFlow()
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    private val _selectedCategory = MutableStateFlow<String?>(null)
    val selectedCategory: StateFlow<String?> = _selectedCategory.asStateFlow()
    
    val products = combine(
        _searchQuery,
        _selectedCategory
    ) { query, category ->
        when {
            query.isNotBlank() -> productRepository.searchProducts(query)
            category != null -> productRepository.getProductsByCategory(category)
            else -> productRepository.getAllActiveProducts()
        }
    }.flatMapLatest { it }
    
    val categories = productRepository.getCategories()
    
    init {
        loadProducts()
        loadCategories()
        // Load remote products on app start
        loadRemoteProducts()
    }
    
    private fun loadProducts() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                productRepository.getAllProducts().collect { products ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        products = products,
                        filteredProducts = filterProducts(products, _uiState.value.selectedCategory, _uiState.value.searchQuery)
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }

    private fun loadCategories() {
        viewModelScope.launch {
            try {
                val products = productRepository.getAllProducts()
                products.collect { productList ->
                    val categories = if (productList.isNotEmpty()) {
                        listOf("All") + productList.map { it.category }.distinct().sorted()
                    } else {
                        listOf("All") // Default when no products are loaded yet
                    }
                    _uiState.value = _uiState.value.copy(categories = categories)
                }
            } catch (e: Exception) {
                // Handle error silently for categories
                _uiState.value = _uiState.value.copy(categories = listOf("All"))
            }
        }
    }

    /**
     * Load products from remote MySQL database and sync with local database
     */
    private fun loadRemoteProducts() {
        viewModelScope.launch {
            try {
                println("Starting remote product sync...")
                remoteProductRepository.getProducts(
                    activeOnly = true,
                    limit = 10000 // Set a very high limit to get all products
                ).collect { result ->
                    when (result) {
                        is NetworkResult.Loading -> {
                            println("Loading remote products...")
                            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                        }
                        is NetworkResult.Success -> {
                            println("Successfully loaded ${result.data.size} remote products")
                            // Convert remote products to local products and save to local database
                            val localProducts = result.data.map { it.toLocalProduct() }

                            // Upsert products (update existing, insert new)
                            var insertedCount = 0
                            var updatedCount = 0

                            localProducts.forEach { product ->
                                try {
                                    // Check if product already exists by product code
                                    val existingProduct = productRepository.getProductByProductCode(product.productCode)

                                    if (existingProduct != null) {
                                        // Update existing product
                                        val updatedProduct = product.copy(
                                            id = existingProduct.id, // Keep the existing ID
                                            createdAt = existingProduct.createdAt, // Keep original creation time
                                            updatedAt = System.currentTimeMillis() // Update the modification time
                                        )
                                        productRepository.updateProduct(updatedProduct)
                                        updatedCount++
                                        println("Updated product: ${product.name}")
                                    } else {
                                        // Insert new product
                                        productRepository.insertProduct(product)
                                        insertedCount++
                                        println("Inserted new product: ${product.name}")
                                    }
                                } catch (e: Exception) {
                                    println("Failed to sync product ${product.name}: ${e.message}")
                                }
                            }

                            val totalSynced = insertedCount + updatedCount
                            println("Sync completed: $insertedCount new, $updatedCount updated")

                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = if (totalSynced > 0) {
                                    "✅ Products synced successfully: $insertedCount new, $updatedCount updated"
                                } else {
                                    "✅ All products are up to date - no changes needed"
                                }
                            )

                            // Reload local products and categories after sync
                            if (totalSynced > 0) {
                                loadProducts()
                                loadCategories()
                            }
                        }
                        is NetworkResult.Error -> {
                            println("Error loading remote products: ${result.message}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = "Failed to load remote products: ${result.message}"
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                println("Exception in loadRemoteProducts: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Sync failed: ${e.message}"
                )
            }
        }
    }

    /**
     * Refresh products from remote database
     */
    fun refreshFromRemote() {
        loadRemoteProducts()
    }

    /**
     * Clear all local products (for testing)
     */
    fun clearAllProducts() {
        viewModelScope.launch {
            try {
                // Note: You would need to add this method to ProductRepository
                // productRepository.deleteAllProducts()
                println("Clear all products - method not implemented yet")
                _uiState.value = _uiState.value.copy(
                    error = "Clear function not implemented - products will be updated instead of duplicated"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to clear products: ${e.message}"
                )
            }
        }
    }



    private fun filterProducts(products: List<Product>, category: String?, searchQuery: String): List<Product> {
        var filtered = products

        // Filter by category
        if (category != null && category != "All") {
            filtered = filtered.filter { it.category == category }
        }

        // Filter by search query
        if (searchQuery.isNotBlank()) {
            filtered = filtered.filter {
                it.name.contains(searchQuery, ignoreCase = true) ||
                it.description.contains(searchQuery, ignoreCase = true) ||
                it.category.contains(searchQuery, ignoreCase = true)
            }
        }

        return filtered
    }
    
    fun searchProducts(query: String) {
        _searchQuery.value = query
        _uiState.value = _uiState.value.copy(
            searchQuery = query,
            filteredProducts = filterProducts(_uiState.value.products, _uiState.value.selectedCategory, query)
        )
    }

    fun selectCategory(category: String?) {
        _selectedCategory.value = category
    }

    fun filterByCategory(category: String) {
        val selectedCategory = if (category == "All") null else category
        _uiState.value = _uiState.value.copy(
            selectedCategory = selectedCategory,
            filteredProducts = filterProducts(_uiState.value.products, selectedCategory, _uiState.value.searchQuery)
        )
    }
    
    fun addProduct(
        name: String,
        description: String,
        price: BigDecimal,
        category: String,
        productCode: String,
        barcode: String,
        stockQuantity: Int
    ) {
        viewModelScope.launch {
            try {
                val product = Product(
                    name = name,
                    description = description,
                    price = price,
                    category = category,
                    productCode = productCode,
                    barcode = barcode,
                    stockQuantity = stockQuantity
                )
                productRepository.insertProduct(product)
                _uiState.value = _uiState.value.copy(showAddDialog = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = e.message)
            }
        }
    }
    
    fun updateProduct(product: Product) {
        viewModelScope.launch {
            try {
                productRepository.updateProduct(product)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = e.message)
            }
        }
    }
    
    fun deleteProduct(product: Product) {
        viewModelScope.launch {
            try {
                productRepository.deactivateProduct(product.id)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = e.message)
            }
        }
    }
    
    fun showAddDialog() {
        _uiState.value = _uiState.value.copy(showAddDialog = true)
    }
    
    fun hideAddDialog() {
        _uiState.value = _uiState.value.copy(showAddDialog = false)
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    suspend fun getProductByBarcode(barcode: String): Product? {
        return productRepository.getProductByBarcode(barcode)
    }

    suspend fun getProductByProductCode(productCode: String): Product? {
        return productRepository.getProductByProductCode(productCode)
    }
}

data class ProductsUiState(
    val isLoading: Boolean = false,
    val showAddDialog: Boolean = false,
    val products: List<Product> = emptyList(),
    val filteredProducts: List<Product> = emptyList(),
    val categories: List<String> = emptyList(),
    val selectedCategory: String? = null,
    val searchQuery: String = "",
    val error: String? = null
)
