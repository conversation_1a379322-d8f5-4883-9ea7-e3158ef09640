package com.intelligent.evolvaordercollection.ui.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

sealed class Screen(
    val route: String,
    val title: String,
    val icon: ImageVector
) {
    object Dashboard : Screen(
        route = "dashboard",
        title = "Dashboard",
        icon = Icons.Default.Dashboard
    )

    object Products : Screen(
        route = "products",
        title = "Products",
        icon = Icons.Default.Inventory
    )

    object Customers : Screen(
        route = "customers",
        title = "Customers",
        icon = Icons.Default.People
    )

    object OrderEntry : Screen(
        route = "order_entry",
        title = "Order Entry",
        icon = Icons.Default.ShoppingCart
    )

    object Orders : Screen(
        route = "orders",
        title = "Orders",
        icon = Icons.Default.Assignment
    )

    object Reports : Screen(
        route = "reports",
        title = "Reports",
        icon = Icons.Default.Assessment
    )

    companion object {
        val bottomNavItems = listOf(
            Dashboard,
            Products,
            Customers,
            OrderEntry,
            Orders,
            Reports
        )
    }
}
