package com.intelligent.evolvaordercollection.ui.screens.orders

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.intelligent.evolvaordercollection.data.model.*
import com.intelligent.evolvaordercollection.ui.navigation.Screen
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderManagementViewModel
import com.intelligent.evolvaordercollection.utils.CurrencyUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderManagementDashboard(
    navController: NavController,
    viewModel: OrderManagementViewModel
) {
    val uiState by viewModel.uiState.collectAsState()
    val alerts by viewModel.alerts.collectAsState()
    val performanceMetrics by viewModel.performanceMetrics.collectAsState()
    
    var showFilterDialog by remember { mutableStateOf(false) }
    var showExportDialog by remember { mutableStateOf(false) }
    var selectedWidget by remember { mutableStateOf<DashboardWidget?>(null) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    )
                )
            )
    ) {
        // Top App Bar with Actions
        TopAppBar(
            title = {
                Text(
                    "Dashboard",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                // Search
                IconButton(onClick = { /* Show search */ }) {
                    Icon(Icons.Default.Search, contentDescription = "Search")
                }
                
                // Filter
                IconButton(onClick = { showFilterDialog = true }) {
                    Icon(Icons.Default.FilterList, contentDescription = "Filter")
                }
                
                // Export
                IconButton(onClick = { showExportDialog = true }) {
                    Icon(Icons.Default.FileDownload, contentDescription = "Export")
                }
                
                // Settings
                IconButton(onClick = { /* Show settings */ }) {
                    Icon(Icons.Default.Settings, contentDescription = "Settings")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            )
        )

        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp) // Reduced from 16.dp to 12.dp
            ) {
                // Quick Stats Row
                item {
                    QuickStatsRow(performanceMetrics)
                }

                // Alert Banner (if any critical alerts)
                if (alerts.any { it.priority == AlertPriority.CRITICAL && !it.isRead }) {
                    item {
                        CriticalAlertBanner(
                            alerts = alerts.filter { it.priority == AlertPriority.CRITICAL && !it.isRead },
                            onAlertClick = { alert -> viewModel.markAlertAsRead(alert.id) }
                        )
                    }
                }

                // Dashboard Widgets Grid
                item {
                    LazyVerticalGrid(
                        columns = GridCells.Adaptive(minSize = 300.dp),
                        modifier = Modifier.height(500.dp), // Further reduced from 600.dp to 500.dp
                        horizontalArrangement = Arrangement.spacedBy(10.dp), // Further reduced spacing
                        verticalArrangement = Arrangement.spacedBy(10.dp)   // Further reduced spacing
                    ) {
                        items(uiState.dashboardWidgets.filter { it.isVisible }) { widget ->
                            DashboardWidgetCard(
                                widget = widget,
                                uiState = uiState,
                                alerts = alerts,
                                performanceMetrics = performanceMetrics,
                                onWidgetClick = { selectedWidget = widget },
                                onNavigateToOrderEntry = {
                                    navController.navigate(Screen.OrderEntry.route)
                                },
                                onNavigateToReports = {
                                    navController.navigate(Screen.Reports.route)
                                },
                                onOrderClick = { order ->
                                    viewModel.selectOrder(order)
                                }
                            )
                        }
                    }
                }


            }
        }
    }

    // Dialogs
    if (showFilterDialog) {
        FilterDialog(
            currentFilter = uiState.currentFilter,
            onFilterApply = { filter ->
                viewModel.applyFilter(filter)
                showFilterDialog = false
            },
            onDismiss = { showFilterDialog = false }
        )
    }

    if (showExportDialog) {
        ExportDialog(
            onExport = { config ->
                viewModel.exportData(config)
                showExportDialog = false
            },
            onDismiss = { showExportDialog = false }
        )
    }

    // Order Details Dialog
    uiState.selectedOrderDetails?.let { selectedOrder ->
        if (uiState.showOrderDetailsDialog) {
            OrderDetailsDialog(
                order = selectedOrder,
                orderItems = uiState.selectedOrderItems,
                onDismiss = { viewModel.hideOrderDetailsDialog() },
                onStatusChange = { newStatus ->
                    viewModel.updateOrderStatus(selectedOrder.id, newStatus)
                    viewModel.hideOrderDetailsDialog()
                }
            )
        }
    }

    // Error Handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show error snackbar
        }
    }

    // Export Completion
    if (uiState.exportCompleted) {
        LaunchedEffect(Unit) {
            // Show success message
            viewModel.clearExportCompleted()
        }
    }
}

@Composable
fun QuickStatsRow(metrics: PerformanceMetrics) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(horizontal = 4.dp)
    ) {
        items(getQuickStats(metrics)) { stat ->
            QuickStatCard(stat)
        }
    }
}

@Composable
fun QuickStatCard(stat: QuickStat) {
    Card(
        modifier = Modifier
            .width(140.dp)
            .height(100.dp), // Increased back to 100.dp to show full text
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = stat.backgroundColor
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(10.dp), // Slightly reduced padding to fit content better
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Icon(
                    imageVector = stat.icon,
                    contentDescription = stat.title,
                    tint = stat.iconColor,
                    modifier = Modifier.size(20.dp)
                )
                
                if (stat.trend != null) {
                    Text(
                        text = stat.trend,
                        style = MaterialTheme.typography.labelSmall,
                        color = if (stat.trend.startsWith("+")) 
                            MaterialTheme.colorScheme.primary 
                        else MaterialTheme.colorScheme.error
                    )
                }
            }
            
            Column(
                verticalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                Text(
                    text = stat.value,
                    style = MaterialTheme.typography.titleLarge, // Changed from headlineSmall to titleLarge for better fit
                    fontWeight = FontWeight.Bold,
                    color = stat.valueColor,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = stat.title,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 2, // Allow 2 lines for longer titles
                    overflow = TextOverflow.Ellipsis,
                    lineHeight = 14.sp
                )
            }
        }
    }
}

@Composable
fun CriticalAlertBanner(
    alerts: List<OrderAlert>,
    onAlertClick: (OrderAlert) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = "Critical Alert",
                    tint = MaterialTheme.colorScheme.error,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Critical Alerts (${alerts.size})",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            alerts.take(3).forEach { alert ->
                Text(
                    text = "• ${alert.title}: ${alert.message}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier
                        .clickable { onAlertClick(alert) }
                        .padding(vertical = 2.dp)
                )
            }
            
            if (alerts.size > 3) {
                Text(
                    text = "... and ${alerts.size - 3} more",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.7f)
                )
            }
        }
    }
}

data class QuickStat(
    val title: String,
    val value: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val backgroundColor: Color,
    val iconColor: Color,
    val valueColor: Color,
    val trend: String? = null
)

fun getQuickStats(metrics: PerformanceMetrics): List<QuickStat> {
    return listOf(
        QuickStat(
            title = "Total Orders",
            value = metrics.totalOrders.toString(),
            icon = Icons.Default.ShoppingCart,
            backgroundColor = Color(0xFFE3F2FD),
            iconColor = Color(0xFF1976D2),
            valueColor = Color(0xFF1976D2),
            trend = "+12%"
        ),
        QuickStat(
            title = "Success",
            value = metrics.successOrders.toString(),
            icon = Icons.Default.CheckCircle,
            backgroundColor = Color(0xFFE8F5E8),
            iconColor = Color(0xFF4CAF50),
            valueColor = Color(0xFF4CAF50),
            trend = "+8%"
        ),
        QuickStat(
            title = "Waiting",
            value = metrics.waitingOrders.toString(),
            icon = Icons.Default.Schedule,
            backgroundColor = Color(0xFFFFF3E0),
            iconColor = Color(0xFFFF9800),
            valueColor = Color(0xFFFF9800),
            trend = "-5%"
        ),
        QuickStat(
            title = "Success Rate",
            value = "${metrics.successRate.toInt()}%",
            icon = Icons.Default.TrendingUp,
            backgroundColor = Color(0xFFF3E5F5),
            iconColor = Color(0xFF9C27B0),
            valueColor = Color(0xFF9C27B0),
            trend = "+3%"
        ),
        QuickStat(
            title = "Revenue",
            value = CurrencyUtils.formatPrice(metrics.totalRevenue),
            icon = Icons.Default.AttachMoney,
            backgroundColor = Color(0xFFE0F2F1),
            iconColor = Color(0xFF00796B),
            valueColor = Color(0xFF00796B),
            trend = "+15%"
        )
    )
}
