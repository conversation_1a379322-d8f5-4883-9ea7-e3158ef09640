package com.intelligent.evolvaordercollection.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.intelligent.evolvaordercollection.data.repository.ExportFormat
import com.intelligent.evolvaordercollection.data.repository.ExportRepository
import com.intelligent.evolvaordercollection.data.repository.SyncRepository
import com.intelligent.evolvaordercollection.sync.SyncManager
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class SyncViewModel(
    private val syncRepository: SyncRepository,
    private val exportRepository: ExportRepository,
    private val syncManager: SyncManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SyncUiState())
    val uiState: StateFlow<SyncUiState> = _uiState.asStateFlow()
    
    init {
        loadSyncStatus()
        loadSyncSettings()
    }
    
    private fun loadSyncStatus() {
        viewModelScope.launch {
            try {
                val status = syncRepository.getSyncStatus()
                _uiState.value = _uiState.value.copy(
                    isOnline = status.isOnline,
                    lastSyncTime = status.lastSyncTime,
                    pendingChanges = status.pendingChanges,
                    deviceId = status.deviceId
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to load sync status: ${e.message}"
                )
            }
        }
    }
    
    private fun loadSyncSettings() {
        _uiState.value = _uiState.value.copy(
            autoSyncEnabled = syncManager.isAutoSyncEnabled(),
            syncInterval = syncManager.getSyncInterval()
        )
    }
    
    fun triggerManualSync() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isSyncing = true,
                syncProgress = 0,
                syncMessage = "Starting sync...",
                errorMessage = "",
                successMessage = ""
            )
            
            try {
                syncRepository.performFullSync().collect { progress ->
                    _uiState.value = _uiState.value.copy(
                        syncProgress = progress.progress,
                        syncMessage = progress.message
                    )
                    
                    if (progress.isError) {
                        _uiState.value = _uiState.value.copy(
                            isSyncing = false,
                            errorMessage = progress.message
                        )
                    } else if (progress.isComplete) {
                        _uiState.value = _uiState.value.copy(
                            isSyncing = false,
                            successMessage = "Sync completed successfully!",
                            lastSyncTime = System.currentTimeMillis()
                        )
                        loadSyncStatus() // Refresh status
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSyncing = false,
                    errorMessage = "Sync failed: ${e.message}"
                )
            }
        }
    }
    
    fun setAutoSyncEnabled(enabled: Boolean) {
        syncManager.setAutoSyncEnabled(enabled)
        _uiState.value = _uiState.value.copy(
            autoSyncEnabled = enabled,
            successMessage = if (enabled) "Auto sync enabled" else "Auto sync disabled"
        )
        clearMessagesAfterDelay()
    }
    
    fun setSyncInterval(intervalMinutes: Long) {
        syncManager.setSyncInterval(intervalMinutes)
        _uiState.value = _uiState.value.copy(
            syncInterval = intervalMinutes,
            successMessage = "Sync interval updated to $intervalMinutes minutes"
        )
        clearMessagesAfterDelay()
    }
    
    fun exportData(format: ExportFormat) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isExporting = true,
                errorMessage = "",
                successMessage = ""
            )
            
            try {
                val result = when (format) {
                    ExportFormat.CSV -> exportRepository.exportToCSV()
                    ExportFormat.EXCEL -> exportRepository.exportToExcel()
                }
                
                result.fold(
                    onSuccess = { exportResult ->
                        _uiState.value = _uiState.value.copy(
                            isExporting = false,
                            lastExportPath = exportResult.exportPath,
                            successMessage = "Data exported successfully! ${exportResult.totalRecords} records exported to ${exportResult.exportPath}"
                        )
                    },
                    onFailure = { error ->
                        _uiState.value = _uiState.value.copy(
                            isExporting = false,
                            errorMessage = "Export failed: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    errorMessage = "Export failed: ${e.message}"
                )
            }
            
            clearMessagesAfterDelay()
        }
    }
    
    fun exportOrdersByDateRange(startDate: Long, endDate: Long, format: ExportFormat) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isExporting = true,
                errorMessage = "",
                successMessage = ""
            )
            
            try {
                val result = exportRepository.exportOrdersByDateRange(startDate, endDate, format)
                
                result.fold(
                    onSuccess = { exportResult ->
                        _uiState.value = _uiState.value.copy(
                            isExporting = false,
                            lastExportPath = exportResult.exportPath,
                            successMessage = "Orders exported successfully! ${exportResult.totalRecords} records exported."
                        )
                    },
                    onFailure = { error ->
                        _uiState.value = _uiState.value.copy(
                            isExporting = false,
                            errorMessage = "Export failed: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    errorMessage = "Export failed: ${e.message}"
                )
            }
            
            clearMessagesAfterDelay()
        }
    }
    
    fun clearErrorMessage() {
        _uiState.value = _uiState.value.copy(errorMessage = "")
    }
    
    fun clearSuccessMessage() {
        _uiState.value = _uiState.value.copy(successMessage = "")
    }
    
    private fun clearMessagesAfterDelay() {
        viewModelScope.launch {
            kotlinx.coroutines.delay(5000) // Clear messages after 5 seconds
            _uiState.value = _uiState.value.copy(
                errorMessage = "",
                successMessage = ""
            )
        }
    }
    
    fun refreshSyncStatus() {
        loadSyncStatus()
    }
}

data class SyncUiState(
    val isOnline: Boolean = false,
    val lastSyncTime: Long = 0L,
    val pendingChanges: Int = 0,
    val deviceId: String = "",
    val isSyncing: Boolean = false,
    val syncProgress: Int = 0,
    val syncMessage: String = "",
    val autoSyncEnabled: Boolean = true,
    val syncInterval: Long = 15L,
    val isExporting: Boolean = false,
    val lastExportPath: String = "",
    val errorMessage: String = "",
    val successMessage: String = ""
)
