package com.intelligent.evolvaordercollection.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.MenuOpen
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.intelligent.evolvaordercollection.ui.navigation.Screen
import com.intelligent.evolvaordercollection.utils.ResponsiveUtils

@Composable
fun CollapsibleSidebarNavigation(
    navController: NavController,
    currentRoute: String?,
    isExpanded: Boolean,
    onToggleExpanded: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isCompact = ResponsiveUtils.isCompactScreen()
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()
    val responsiveCornerRadius = ResponsiveUtils.getResponsiveCornerRadius()
    
    val sidebarWidth = if (isExpanded) {
        if (isCompact) 200.dp else 240.dp
    } else {
        if (isCompact) 56.dp else 72.dp
    }
    
    Card(
        modifier = modifier
            .width(sidebarWidth)
            .fillMaxHeight(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(
            topEnd = responsiveCornerRadius,
            bottomEnd = responsiveCornerRadius
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = responsivePadding)
        ) {
            // Header with toggle button
            SidebarHeader(
                isExpanded = isExpanded,
                onToggleExpanded = onToggleExpanded,
                isCompact = isCompact,
                responsivePadding = responsivePadding
            )
            
            Spacer(modifier = Modifier.height(responsiveSpacing))
            
            // Navigation items
            Screen.bottomNavItems.forEach { screen ->
                SidebarNavigationItem(
                    screen = screen,
                    isSelected = currentRoute == screen.route,
                    isExpanded = isExpanded,
                    isCompact = isCompact,
                    onClick = {
                        navController.navigate(screen.route) {
                            popUpTo(navController.graph.startDestinationId)
                            launchSingleTop = true
                        }
                    },
                    responsivePadding = responsivePadding,
                    responsiveSpacing = responsiveSpacing
                )
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            // Footer (optional)
            if (isExpanded) {
                SidebarFooter(
                    isCompact = isCompact,
                    responsivePadding = responsivePadding
                )
            }
        }
    }
}

@Composable
private fun SidebarHeader(
    isExpanded: Boolean,
    onToggleExpanded: () -> Unit,
    isCompact: Boolean,
    responsivePadding: Dp
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = responsivePadding),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = if (isExpanded) Arrangement.SpaceBetween else Arrangement.Center
    ) {
        AnimatedVisibility(
            visible = isExpanded,
            enter = slideInHorizontally(
                initialOffsetX = { -it },
                animationSpec = tween(300)
            ),
            exit = slideOutHorizontally(
                targetOffsetX = { -it },
                animationSpec = tween(300)
            )
        ) {
            Text(
                text = "eVolva POS",
                style = if (isCompact) MaterialTheme.typography.titleMedium else MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        IconButton(
            onClick = onToggleExpanded,
            modifier = Modifier.size(if (isCompact) 36.dp else 40.dp)
        ) {
            val rotation by animateFloatAsState(
                targetValue = if (isExpanded) 180f else 0f,
                animationSpec = tween(300),
                label = "MenuRotation"
            )
            
            Icon(
                imageVector = if (isExpanded) Icons.Default.MenuOpen else Icons.Default.Menu,
                contentDescription = if (isExpanded) "Collapse Menu" else "Expand Menu",
                modifier = Modifier
                    .size(if (isCompact) 20.dp else 24.dp)
                    .rotate(rotation),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun SidebarNavigationItem(
    screen: Screen,
    isSelected: Boolean,
    isExpanded: Boolean,
    isCompact: Boolean,
    onClick: () -> Unit,
    responsivePadding: Dp,
    responsiveSpacing: Dp
) {
    val backgroundColor = if (isSelected) {
        MaterialTheme.colorScheme.primaryContainer
    } else {
        Color.Transparent
    }
    
    val contentColor = if (isSelected) {
        MaterialTheme.colorScheme.onPrimaryContainer
    } else {
        MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = responsivePadding * 0.5f)
            .clip(RoundedCornerShape(if (isCompact) 8.dp else 12.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(
                horizontal = responsivePadding,
                vertical = if (isCompact) 8.dp else 12.dp
            )
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = if (isExpanded) Arrangement.Start else Arrangement.Center
        ) {
            Icon(
                imageVector = screen.icon,
                contentDescription = screen.title,
                modifier = Modifier.size(if (isCompact) 18.dp else 22.dp),
                tint = contentColor
            )
            
            AnimatedVisibility(
                visible = isExpanded,
                enter = slideInHorizontally(
                    initialOffsetX = { -it },
                    animationSpec = tween(300)
                ),
                exit = slideOutHorizontally(
                    targetOffsetX = { -it },
                    animationSpec = tween(300)
                )
            ) {
                Row {
                    Spacer(modifier = Modifier.width(responsiveSpacing))
                    Text(
                        text = screen.title,
                        style = if (isCompact) MaterialTheme.typography.bodyMedium else MaterialTheme.typography.bodyLarge,
                        color = contentColor,
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
                    )
                }
            }
        }
    }
    
    Spacer(modifier = Modifier.height(responsiveSpacing * 0.5f))
}

@Composable
private fun SidebarFooter(
    isCompact: Boolean,
    responsivePadding: Dp
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = responsivePadding),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Divider(
            modifier = Modifier.fillMaxWidth(),
            color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
        )
        
        Spacer(modifier = Modifier.height(responsivePadding))
        
        Text(
            text = "v1.0.0",
            style = if (isCompact) MaterialTheme.typography.labelSmall else MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
        )
    }
}

@Composable
fun SidebarOverlay(
    isVisible: Boolean,
    onDismiss: () -> Unit
) {
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInHorizontally(
            initialOffsetX = { -it },
            animationSpec = tween(300)
        ),
        exit = slideOutHorizontally(
            targetOffsetX = { -it },
            animationSpec = tween(300)
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.5f))
                .clickable { onDismiss() }
        )
    }
}
