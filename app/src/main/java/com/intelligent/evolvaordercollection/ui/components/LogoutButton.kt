package com.intelligent.evolvaordercollection.ui.components

import android.content.Context
import android.content.Intent
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Logout
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.intelligent.evolvaordercollection.ui.activities.LoginActivity
import com.intelligent.evolvaordercollection.data.repository.UserSessionManager

@Composable
fun LogoutButton(
    modifier: Modifier = Modifier,
    showText: Boolean = true
) {
    val context = LocalContext.current
    var showLogoutDialog by remember { mutableStateOf(false) }
    
    // Logout confirmation dialog
    if (showLogoutDialog) {
        AlertDialog(
            onDismissRequest = { showLogoutDialog = false },
            title = {
                Text("Logout Confirmation")
            },
            text = {
                Text("Are you sure you want to logout?")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showLogoutDialog = false
                        performLogout(context)
                    }
                ) {
                    Text("Logout")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showLogoutDialog = false }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
    
    if (showText) {
        // Button with text
        OutlinedButton(
            onClick = { showLogoutDialog = true },
            modifier = modifier,
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.error
            )
        ) {
            Icon(
                imageVector = Icons.Default.Logout,
                contentDescription = "Logout",
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Logout",
                fontWeight = FontWeight.Medium
            )
        }
    } else {
        // Icon button only
        IconButton(
            onClick = { showLogoutDialog = true },
            modifier = modifier
        ) {
            Icon(
                imageVector = Icons.Default.Logout,
                contentDescription = "Logout",
                tint = MaterialTheme.colorScheme.error
            )
        }
    }
}

/**
 * Perform logout and navigate to login screen
 */
private fun performLogout(context: Context) {
    try {
        // Clear user session
        val sessionManager = UserSessionManager.getInstance()
        sessionManager.clearSession()
        
        // Navigate to login activity
        val intent = Intent(context, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        context.startActivity(intent)
        
        // Close current activity if it's an activity
        if (context is androidx.activity.ComponentActivity) {
            context.finish()
        }
    } catch (e: Exception) {
        println("Error during logout: ${e.message}")
    }
}

@Composable
fun UserInfoCard(
    modifier: Modifier = Modifier
) {
    val sessionManager = UserSessionManager.getInstance()
    val currentUser = sessionManager.getCurrentUser()
    
    if (currentUser != null) {
        Card(
            modifier = modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Logged in as:",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = currentUser.userName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                if (currentUser.saleTeamName.isNotBlank()) {
                    Text(
                        text = "Team: ${currentUser.saleTeamName}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                LogoutButton(
                    modifier = Modifier.fillMaxWidth(),
                    showText = true
                )
            }
        }
    }
}
