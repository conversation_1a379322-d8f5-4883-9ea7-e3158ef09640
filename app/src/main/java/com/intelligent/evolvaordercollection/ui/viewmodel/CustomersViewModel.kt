package com.intelligent.evolvaordercollection.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.intelligent.evolvaordercollection.data.model.Customer
import com.intelligent.evolvaordercollection.data.model.Township
import com.intelligent.evolvaordercollection.data.model.SaleTeam
import com.intelligent.evolvaordercollection.data.network.NetworkResult
import com.intelligent.evolvaordercollection.data.network.NetworkModule
import com.intelligent.evolvaordercollection.data.network.safeApiCall
import com.intelligent.evolvaordercollection.data.repository.CustomerRepository
import com.intelligent.evolvaordercollection.data.repository.RemoteCustomerRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * ViewModel for managing customers
 */
class CustomersViewModel(
    private val customerRepository: CustomerRepository
) : ViewModel() {
    
    private val remoteCustomerRepository = RemoteCustomerRepository()
    private val apiService = NetworkModule.provideProductApiService()

    private val _uiState = MutableStateFlow(CustomersUiState())
    val uiState: StateFlow<CustomersUiState> = _uiState.asStateFlow()

    private val _searchQuery = MutableStateFlow("")
    private val _selectedTownship = MutableStateFlow<Int?>(null)
    
    init {
        loadCustomers()
        loadTownships()
        observeSearchAndFilter()
    }
    
    private fun observeSearchAndFilter() {
        viewModelScope.launch {
            combine(
                customerRepository.getAllCustomers(),
                _searchQuery,
                _selectedTownship
            ) { customers, searchQuery, township ->
                filterCustomers(customers, searchQuery, township)
            }.collect { filteredCustomers ->
                _uiState.value = _uiState.value.copy(
                    customers = filteredCustomers,
                    isLoading = false
                )
            }
        }
    }
    
    private fun loadCustomers() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                // Customers are loaded through the flow in observeSearchAndFilter
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load customers: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Sync customers from remote database
     */
    fun syncCustomers() {
        viewModelScope.launch {
            try {
                println("Starting customer sync...")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                remoteCustomerRepository.getCustomers(
                    activeOnly = true,
                    limit = 10000 // Get all customers
                ).collect { result ->
                    when (result) {
                        is NetworkResult.Loading -> {
                            println("Loading remote customers...")
                        }
                        is NetworkResult.Success -> {
                            println("Successfully loaded ${result.data.size} remote customers")

                            // Check how many have location data
                            val customersWithLocation = result.data.filter { customer ->
                                customer.latitude != null && customer.longitude != null &&
                                customer.latitude != 0.0 && customer.longitude != 0.0
                            }
                            println("Customers with location data: ${customersWithLocation.size}/${result.data.size}")

                            // Convert remote customers to local customers and sync
                            val localCustomers = result.data.map { it.toLocalCustomer() }

                            var insertedCount = 0
                            var updatedCount = 0
                            
                            localCustomers.forEach { customer ->
                                try {
                                    // Check if customer already exists by customer code
                                    val existingCustomer = customerRepository.getCustomerByCode(customer.customerCode)
                                    
                                    if (existingCustomer != null) {
                                        // Update existing customer
                                        val updatedCustomer = customer.copy(
                                            id = existingCustomer.id,
                                            createdAt = existingCustomer.createdAt,
                                            updatedAt = System.currentTimeMillis()
                                        )
                                        customerRepository.updateCustomer(updatedCustomer)
                                        updatedCount++
                                        println("Updated customer: ${customer.name}")
                                    } else {
                                        // Insert new customer
                                        customerRepository.insertCustomer(customer)
                                        insertedCount++
                                        println("Inserted new customer: ${customer.name}")
                                    }
                                } catch (e: Exception) {
                                    println("Failed to sync customer ${customer.name}: ${e.message}")
                                }
                            }
                            
                            val totalSynced = insertedCount + updatedCount
                            println("Customer sync completed: $insertedCount new, $updatedCount updated")
                            
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = if (totalSynced > 0) {
                                    "✅ Customers synced: $insertedCount new, $updatedCount updated"
                                } else {
                                    "✅ All customers are up to date"
                                }
                            )
                        }
                        is NetworkResult.Error -> {
                            println("Error loading remote customers: ${result.message}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = "❌ Failed to sync customers: ${result.message}"
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                println("Exception in syncCustomers: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "❌ Customer sync failed: ${e.message}"
                )
            }
        }
    }
    
    fun searchCustomers(query: String) {
        _searchQuery.value = query
    }

    fun filterByTownship(townshipId: Int?) {
        _selectedTownship.value = townshipId
    }
    
    /**
     * Load townships for dropdown filter
     */
    private fun loadTownships() {
        viewModelScope.launch {
            try {
                val result = safeApiCall { apiService.getTownships() }
                when (result) {
                    is NetworkResult.Success -> {
                        val townships = result.data.data ?: emptyList()
                        _uiState.value = _uiState.value.copy(townships = townships)
                    }
                    is NetworkResult.Error -> {
                        println("Failed to load townships: ${result.message}")
                    }
                    is NetworkResult.Loading -> {
                        // Handle loading if needed
                    }
                }
            } catch (e: Exception) {
                println("Exception loading townships: ${e.message}")
            }
        }
    }



    /**
     * Load customers with location data for testing
     */
    fun loadCustomersWithLocation() {
        viewModelScope.launch {
            try {
                println("Loading customers with location data...")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                remoteCustomerRepository.getCustomersWithLocation(limit = 50).collect { result ->
                    when (result) {
                        is NetworkResult.Loading -> {
                            println("Loading customers with location...")
                        }
                        is NetworkResult.Success -> {
                            println("Found ${result.data.size} customers with location data")
                            result.data.forEach { customer ->
                                println("${customer.companyName}: ${customer.latitude}, ${customer.longitude}")
                            }

                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = "✅ Found ${result.data.size} customers with location data"
                            )
                        }
                        is NetworkResult.Error -> {
                            println("Error loading customers with location: ${result.message}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = "❌ Failed to load location data: ${result.message}"
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                println("Exception in loadCustomersWithLocation: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "❌ Location test failed: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    private fun filterCustomers(
        customers: List<Customer>,
        searchQuery: String,
        townshipId: Int?
    ): List<Customer> {
        return customers.filter { customer ->
            val matchesSearch = if (searchQuery.isBlank()) {
                true
            } else {
                customer.name.contains(searchQuery, ignoreCase = true) ||
                customer.customerCode.contains(searchQuery, ignoreCase = true) ||
                customer.email.contains(searchQuery, ignoreCase = true) ||
                customer.phone.contains(searchQuery, ignoreCase = true) ||
                customer.contactPerson.contains(searchQuery, ignoreCase = true) ||
                customer.townshipName.contains(searchQuery, ignoreCase = true)
            }

            val matchesTownship = townshipId == null || customer.townshipId == townshipId

            matchesSearch && matchesTownship && customer.isActive
        }
    }
}

/**
 * UI State for customers screen
 */
data class CustomersUiState(
    val customers: List<Customer> = emptyList(),
    val townships: List<Township> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val searchQuery: String = "",
    val selectedTownshipId: Int? = null
)
