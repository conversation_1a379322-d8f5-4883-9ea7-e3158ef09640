package com.intelligent.evolvaordercollection.ui.screens.orders

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.unit.dp
import androidx.compose.runtime.derivedStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.intelligent.evolvaordercollection.data.model.Product
import com.intelligent.evolvaordercollection.ui.components.BarcodeScannerDialog
import com.intelligent.evolvaordercollection.ui.components.GoogleSearchDialog
import com.intelligent.evolvaordercollection.ui.components.ProductDetailsDialog
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderCollectionUiState
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderCollectionViewModel
import com.intelligent.evolvaordercollection.ui.viewmodel.ProductsUiState
import com.intelligent.evolvaordercollection.ui.viewmodel.ProductsViewModel
import com.intelligent.evolvaordercollection.utils.CurrencyUtils
import com.intelligent.evolvaordercollection.utils.ResponsiveUtils
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InteractiveProductListView(
    productsUiState: ProductsUiState,
    orderUiState: OrderCollectionUiState,
    productsViewModel: ProductsViewModel,
    orderViewModel: OrderCollectionViewModel,
    modifier: Modifier = Modifier,
    isCompact: Boolean = false
) {
    var searchQuery by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf<String?>(null) }
    var selectedFilter by remember { mutableStateOf("All") }
    var showBarcodeScanner by remember { mutableStateOf(false) }
    var showGoogleSearch by remember { mutableStateOf(false) }
    var googleSearchQuery by remember { mutableStateOf("") }
    var selectedProductForDetails by remember { mutableStateOf<Product?>(null) }
    val keyboardController = LocalSoftwareKeyboardController.current
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()
    val scope = rememberCoroutineScope()

    Column(
        modifier = modifier.padding(if (isCompact) 4.dp else responsivePadding * 0.3f) // Further reduced padding for more space
    ) {
        // Enhanced Search Header with integrated filter
        ProductSearchHeader(
            searchQuery = searchQuery,
            onSearchQueryChange = {
                searchQuery = it
                orderViewModel.updateSearchQuery(it)
            },
            onClearSearch = {
                searchQuery = ""
                orderViewModel.updateSearchQuery("")
                keyboardController?.hide()
            },
            onBarcodeScanner = { showBarcodeScanner = true },
            onGoogleSearch = { query ->
                googleSearchQuery = query
                showGoogleSearch = true
            },
            selectedFilter = selectedFilter,
            onFilterSelected = { selectedFilter = it },
            isCompact = isCompact
        )

        Spacer(modifier = Modifier.height(if (isCompact) 2.dp else 3.dp))
        
        // Category Filter Chips
        CategoryFilterSection(
            categories = productsUiState.categories,
            selectedCategory = selectedCategory,
            onCategorySelected = { category ->
                selectedCategory = if (selectedCategory == category) null else category
                productsViewModel.filterByCategory(category ?: "All")
            }
        )
        
        Spacer(modifier = Modifier.height(responsiveSpacing * 0.3f))
        
        // Products Grid with Real-time Updates
        ProductsGridSection(
            products = getFilteredProducts(productsUiState.filteredProducts, searchQuery, selectedFilter),
            orderUiState = orderUiState,
            orderViewModel = orderViewModel,
            onProductInfoClick = { product ->
                println("Product info clicked for: ${product.name}")
                selectedProductForDetails = product
            },
            modifier = Modifier.weight(1f),
            isCompact = isCompact
        )
    }

    // Barcode Scanner Dialog
    if (showBarcodeScanner) {
        BarcodeScannerDialog(
            onBarcodeScanned = { scannedCode ->
                // Search for product by barcode or product code
                scope.launch {
                    val productByBarcode = productsViewModel.getProductByBarcode(scannedCode)
                    val productByCode = productsViewModel.getProductByProductCode(scannedCode)

                    val foundProduct = productByBarcode ?: productByCode
                    foundProduct?.let { product ->
                        if (product.stockQuantity > 0) {
                            orderViewModel.addToCart(product)
                        }
                    }
                }
                showBarcodeScanner = false
            },
            onDismiss = { showBarcodeScanner = false }
        )
    }

    // Product Details Dialog
    selectedProductForDetails?.let { product ->
        println("Showing ProductDetailsDialog for: ${product.name}")
        ProductDetailsDialog(
            product = product,
            onDismiss = {
                println("Dismissing ProductDetailsDialog")
                selectedProductForDetails = null
            }
        )
    }

    // Google Search Dialog
    if (showGoogleSearch) {
        GoogleSearchDialog(
            searchQuery = googleSearchQuery,
            onDismiss = { showGoogleSearch = false }
        )
    }
}

@Composable
fun ProductSearchHeader(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onClearSearch: () -> Unit,
    onBarcodeScanner: () -> Unit,
    onGoogleSearch: (String) -> Unit,
    selectedFilter: String,
    onFilterSelected: (String) -> Unit,
    isCompact: Boolean = false
) {
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveCornerRadius = ResponsiveUtils.getResponsiveCornerRadius()

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(6.dp), // Reduced spacing
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Filter Dropdown
        ProductFilterDropdown(
            selectedFilter = selectedFilter,
            onFilterSelected = onFilterSelected,
            isCompact = isCompact
        )

        // Search TextField
        OutlinedTextField(
            value = searchQuery,
            onValueChange = onSearchQueryChange,
            modifier = Modifier
                .weight(1f)
                .height(if (isCompact) 40.dp else 48.dp),
            placeholder = {
            Text(
                if (isCompact) "Search products..." else "Search products by name, category...",
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                style = if (isCompact) MaterialTheme.typography.bodySmall else MaterialTheme.typography.bodyMedium
            )
        },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "Search",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(if (isCompact) 18.dp else 20.dp)
            )
        },
        trailingIcon = {
            if (searchQuery.isNotEmpty()) {
                IconButton(onClick = onClearSearch) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "Clear search",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(if (isCompact) 16.dp else 18.dp)
                    )
                }
            }
        },
        singleLine = true,
        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
        keyboardActions = KeyboardActions(
            onSearch = { /* Handle search action */ }
        ),
        shape = RoundedCornerShape(responsiveCornerRadius),
        colors = OutlinedTextFieldDefaults.colors(
            focusedBorderColor = MaterialTheme.colorScheme.primary,
            unfocusedBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
        ),
        textStyle = if (isCompact) MaterialTheme.typography.bodyMedium else MaterialTheme.typography.bodyLarge
    )

        // Barcode Scanner Button
        IconButton(
            onClick = onBarcodeScanner,
            modifier = Modifier.size(if (isCompact) 40.dp else 48.dp)
        ) {
            Icon(
                imageVector = Icons.Default.QrCode,
                contentDescription = "Scan Barcode",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(if (isCompact) 20.dp else 24.dp)
            )
        }

        // Google Search Button
        IconButton(
            onClick = {
                if (searchQuery.isNotEmpty()) {
                    onGoogleSearch(searchQuery)
                }
            },
            enabled = searchQuery.isNotEmpty(),
            modifier = Modifier.size(if (isCompact) 40.dp else 48.dp)
        ) {
            GoogleIcon(
                enabled = searchQuery.isNotEmpty(),
                size = if (isCompact) 20.dp else 24.dp
            )
        }
    }
}

@Composable
fun CategoryFilterSection(
    categories: List<String>,
    selectedCategory: String?,
    onCategorySelected: (String) -> Unit
) {
    if (categories.isNotEmpty()) {
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(categories) { category ->
                CategoryChip(
                    category = category,
                    isSelected = selectedCategory == category,
                    onClick = { onCategorySelected(category) }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryChip(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val animatedScale by animateFloatAsState(
        targetValue = if (isSelected) 1.05f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "ChipScale"
    )
    
    FilterChip(
        onClick = onClick,
        label = { 
            Text(
                text = category,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium
            )
        },
        selected = isSelected,
        modifier = Modifier.scale(animatedScale),
        leadingIcon = if (isSelected) {
            {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
            }
        } else null,
        shape = RoundedCornerShape(20.dp),
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary,
            selectedLabelColor = MaterialTheme.colorScheme.onPrimary
        )
    )
}

@Composable
fun ProductsGridSection(
    products: List<Product>,
    orderUiState: OrderCollectionUiState,
    orderViewModel: OrderCollectionViewModel,
    onProductInfoClick: (Product) -> Unit,
    modifier: Modifier = Modifier,
    isCompact: Boolean = false
) {
    val productCardMinSize = ResponsiveUtils.getProductCardMinSize()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()
    if (products.isEmpty()) {
        // Empty state
        Box(
            modifier = modifier,
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "No products found",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = "Try adjusting your search or filter criteria",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center
                )
            }
        }
    } else {
        LazyVerticalGrid(
            columns = GridCells.Adaptive(minSize = productCardMinSize),
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(responsiveSpacing * 0.2f), // Even more reduced spacing
            horizontalArrangement = Arrangement.spacedBy(responsiveSpacing * 0.2f), // Even more reduced spacing
            contentPadding = PaddingValues(bottom = responsiveSpacing * 0.2f)
        ) {
            items(products, key = { it.id }) { product ->
                val cartQuantity by remember(orderUiState.cartItems) {
                    derivedStateOf { orderViewModel.getCartQuantity(product.id) }
                }
                QuantityDrivenProductCard(
                    product = product,
                    cartQuantity = cartQuantity,
                    onProductTap = {
                        if (product.stockQuantity > 0) {
                            orderViewModel.addToCart(product)
                        }
                    },
                    onInfoClick = { onProductInfoClick(product) },
                    isCompact = isCompact
                )
            }
        }
    }
}

@Composable
fun ProductFilterRow(
    selectedFilter: String,
    onFilterSelected: (String) -> Unit,
    isCompact: Boolean = false
) {
    val filters = listOf("All", "Favourites", "In Stock", "Low Stock", "New Arrivals")

    LazyRow(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(if (isCompact) 6.dp else 8.dp),
        contentPadding = PaddingValues(horizontal = if (isCompact) 2.dp else 4.dp)
    ) {
        items(filters) { filter ->
            FilterChip(
                selected = selectedFilter == filter,
                onClick = { onFilterSelected(filter) },
                enabled = true,
                label = {
                    Text(
                        text = filter,
                        style = if (isCompact) MaterialTheme.typography.labelSmall else MaterialTheme.typography.labelMedium,
                        fontWeight = if (selectedFilter == filter) FontWeight.Bold else FontWeight.Normal
                    )
                },
                leadingIcon = {
                    when (filter) {
                        "All" -> Icon(
                            imageVector = Icons.Default.Apps,
                            contentDescription = null,
                            modifier = Modifier.size(if (isCompact) 14.dp else 16.dp)
                        )
                        "Favourites" -> Icon(
                            imageVector = Icons.Default.Favorite,
                            contentDescription = null,
                            modifier = Modifier.size(if (isCompact) 14.dp else 16.dp)
                        )
                        "In Stock" -> Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            modifier = Modifier.size(if (isCompact) 14.dp else 16.dp)
                        )
                        "Low Stock" -> Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = null,
                            modifier = Modifier.size(if (isCompact) 14.dp else 16.dp)
                        )
                        "New Arrivals" -> Icon(
                            imageVector = Icons.Default.NewReleases,
                            contentDescription = null,
                            modifier = Modifier.size(if (isCompact) 14.dp else 16.dp)
                        )
                    }
                },
                colors = FilterChipDefaults.filterChipColors(
                    selectedContainerColor = MaterialTheme.colorScheme.primary,
                    selectedLabelColor = MaterialTheme.colorScheme.onPrimary,
                    selectedLeadingIconColor = MaterialTheme.colorScheme.onPrimary
                ),
                border = if (selectedFilter == filter) null else FilterChipDefaults.filterChipBorder(
                    enabled = true,
                    selected = false,
                    borderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
                )
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductFilterDropdown(
    selectedFilter: String,
    onFilterSelected: (String) -> Unit,
    isCompact: Boolean = false
) {
    val filters = listOf("All", "Favourites", "In Stock", "Low Stock", "New Arrivals")
    var expanded by remember { mutableStateOf(false) }

    Box {
        OutlinedButton(
            onClick = { expanded = true },
            modifier = Modifier
                .width(if (isCompact) 90.dp else 110.dp)
                .height(if (isCompact) 40.dp else 48.dp),
            contentPadding = PaddingValues(horizontal = if (isCompact) 8.dp else 12.dp),
            shape = RoundedCornerShape(ResponsiveUtils.getResponsiveCornerRadius()),
            border = BorderStroke(
                1.dp,
                MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
            )
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                // Filter icon based on selection
                Icon(
                    imageVector = when (selectedFilter) {
                        "All" -> Icons.Default.Apps
                        "Favourites" -> Icons.Default.Favorite
                        "In Stock" -> Icons.Default.CheckCircle
                        "Low Stock" -> Icons.Default.Warning
                        "New Arrivals" -> Icons.Default.NewReleases
                        else -> Icons.Default.Apps
                    },
                    contentDescription = null,
                    modifier = Modifier.size(if (isCompact) 14.dp else 16.dp),
                    tint = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.width(4.dp))

                // Dropdown arrow
                Icon(
                    imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = "Expand",
                    modifier = Modifier.size(if (isCompact) 16.dp else 18.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier.width(if (isCompact) 140.dp else 160.dp)
        ) {
            filters.forEach { filter ->
                DropdownMenuItem(
                    text = {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = when (filter) {
                                    "All" -> Icons.Default.Apps
                                    "Favourites" -> Icons.Default.Favorite
                                    "In Stock" -> Icons.Default.CheckCircle
                                    "Low Stock" -> Icons.Default.Warning
                                    "New Arrivals" -> Icons.Default.NewReleases
                                    else -> Icons.Default.Apps
                                },
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                tint = if (selectedFilter == filter)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = filter,
                                style = if (isCompact) MaterialTheme.typography.bodySmall else MaterialTheme.typography.bodyMedium,
                                fontWeight = if (selectedFilter == filter) FontWeight.Bold else FontWeight.Normal,
                                color = if (selectedFilter == filter)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.onSurface
                            )
                        }
                    },
                    onClick = {
                        onFilterSelected(filter)
                        expanded = false
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuantityDrivenProductCard(
    product: Product,
    cartQuantity: Int,
    onProductTap: () -> Unit,
    onInfoClick: () -> Unit,
    isCompact: Boolean = false
) {
    val responsiveCornerRadius = ResponsiveUtils.getResponsiveCornerRadius()
    val responsivePadding = ResponsiveUtils.getResponsivePadding()
    val responsiveSpacing = ResponsiveUtils.getResponsiveSpacing()
    val haptic = LocalHapticFeedback.current
    var isPressed by remember { mutableStateOf(false) }
    
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "CardPress"
    )
    
    val animatedElevation by animateDpAsState(
        targetValue = if (cartQuantity > 0) 8.dp else 4.dp,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "CardElevation"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(if (isCompact) 100.dp else 120.dp) // Even more reduced height
            .scale(animatedScale),
        elevation = CardDefaults.cardElevation(defaultElevation = animatedElevation),
        shape = RoundedCornerShape(responsiveCornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = if (cartQuantity > 0)
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            else
                MaterialTheme.colorScheme.surface
        ),
        onClick = {
            if (product.stockQuantity > 0) {
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                onProductTap()
            }
        }
    ) {
        // Full image with overlays
        Box(modifier = Modifier.fillMaxSize()) {
            // Full product image
            AsyncImage(
                model = if (product.imageUrl.isNotBlank()) {
                    product.imageUrl
                } else {
                    getProductImageUrl(product.category)
                },
                contentDescription = product.name,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(responsiveCornerRadius)),
                contentScale = ContentScale.Crop
            )

            // Gradient overlay for better text readability
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        Brush.verticalGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.Black.copy(alpha = 0.6f)
                            ),
                            startY = 0f,
                            endY = Float.POSITIVE_INFINITY
                        )
                    )
            )

            // Product name and price overlay at bottom
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .padding(
                        horizontal = if (isCompact) 4.dp else 6.dp,
                        vertical = if (isCompact) 2.dp else 4.dp
                    )
            ) {
                Text(
                    text = product.name,
                    style = if (isCompact) MaterialTheme.typography.bodySmall else MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    maxLines = if (isCompact) 1 else 2,
                    overflow = TextOverflow.Ellipsis
                )

                Text(
                    text = CurrencyUtils.formatPrice(product.price),
                    style = if (isCompact) MaterialTheme.typography.bodySmall else MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.Yellow, // Bright yellow for better visibility on image
                    maxLines = 1
                )
            }

            // Quantity badge at top start
            if (cartQuantity > 0) {
                Surface(
                    modifier = Modifier
                        .padding(2.dp)
                        .align(Alignment.TopStart),
                    color = MaterialTheme.colorScheme.primary,
                    shape = CircleShape,
                    shadowElevation = if (isCompact) 1.dp else 2.dp
                ) {
                    Text(
                        text = cartQuantity.toString(),
                        modifier = Modifier.padding(
                            horizontal = if (isCompact) 4.dp else 6.dp,
                            vertical = if (isCompact) 1.dp else 2.dp
                        ),
                        style = if (isCompact) MaterialTheme.typography.labelSmall else MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            // Beautiful Info icon at top end
            Box(
                modifier = Modifier
                    .padding(if (isCompact) 4.dp else 6.dp)
                    .align(Alignment.TopEnd)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) {
                        println("Info icon clicked!")
                        onInfoClick()
                    }
            ) {
                Surface(
                    modifier = Modifier
                        .size(if (isCompact) 28.dp else 32.dp),
                    color = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f),
                    shape = CircleShape,
                    shadowElevation = if (isCompact) 2.dp else 4.dp,
                    border = BorderStroke(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = "Product Details",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(if (isCompact) 14.dp else 16.dp)
                        )
                    }
                }
            }

                // Subtle glow effect
                Surface(
                    modifier = Modifier
                        .size(if (isCompact) 28.dp else 32.dp)
                        .alpha(0.1f),
                    color = MaterialTheme.colorScheme.primary,
                    shape = CircleShape
                ) {}
            }
            
            // Stock indicator overlay - centered
            if (product.stockQuantity <= 10) {
                if (product.stockQuantity == 0) {
                    // Out of Stock overlay
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
                                RoundedCornerShape(responsiveCornerRadius)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Out of Stock",
                            style = if (isCompact) MaterialTheme.typography.bodyMedium else MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                } else {
                    // Low Stock indicator - centered
                    Box(
                        modifier = Modifier
                            .fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Surface(
                            color = MaterialTheme.colorScheme.tertiary,
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                text = "Low Stock",
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onTertiary,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
        }
    }



// Helper function to filter products
private fun getFilteredProducts(products: List<Product>, searchQuery: String, selectedFilter: String): List<Product> {
    // First apply filter-based filtering
    val filterFiltered = when (selectedFilter) {
        "All" -> products
        "Favourites" -> products.filter { it.category.contains("favourite", ignoreCase = true) || it.name.contains("premium", ignoreCase = true) }
        "In Stock" -> products.filter { it.stockQuantity > 10 }
        "Low Stock" -> products.filter { it.stockQuantity in 1..10 }
        "New Arrivals" -> products.filter {
            val currentTime = System.currentTimeMillis()
            val thirtyDaysAgo = currentTime - (30 * 24 * 60 * 60 * 1000L)
            it.createdAt > thirtyDaysAgo
        }
        else -> products
    }

    // Then apply search query filtering
    return if (searchQuery.isBlank()) {
        filterFiltered
    } else {
        filterFiltered.filter { product ->
            product.name.contains(searchQuery, ignoreCase = true) ||
            product.description.contains(searchQuery, ignoreCase = true) ||
            product.category.contains(searchQuery, ignoreCase = true) ||
            product.productCode.contains(searchQuery, ignoreCase = true) ||
            product.barcode.contains(searchQuery, ignoreCase = true)
        }
    }
}

@Composable
fun GoogleIcon(
    enabled: Boolean,
    size: Dp
) {
    Box(
        modifier = Modifier.size(size),
        contentAlignment = Alignment.Center
    ) {
        // Google "G" with proper colors
        Text(
            text = "G",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = if (enabled) Color(0xFF4285F4) else Color(0xFFBDBDBD), // Google blue when enabled, gray when disabled
            fontSize = (size.value * 0.8).sp
        )
    }
}
