package com.intelligent.evolvaordercollection.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.intelligent.evolvaordercollection.POSApplication
import com.intelligent.evolvaordercollection.ui.screens.orders.AdvancedOrderEntryScreen
import com.intelligent.evolvaordercollection.ui.screens.orders.OrdersScreen
import com.intelligent.evolvaordercollection.ui.screens.orders.OrderManagementDashboard
import com.intelligent.evolvaordercollection.ui.screens.products.ProductsScreen
import com.intelligent.evolvaordercollection.ui.screens.customers.CustomersScreen
import com.intelligent.evolvaordercollection.ui.screens.reports.ReportsScreen
import com.intelligent.evolvaordercollection.ui.screens.sync.SyncSettingsScreen
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderManagementViewModel
import com.intelligent.evolvaordercollection.ui.viewmodel.ViewModelFactory
import com.intelligent.evolvaordercollection.ui.viewmodel.CustomersViewModel

@Composable
fun POSNavigation(navController: NavHostController) {
    NavHost(
        navController = navController,
        startDestination = Screen.Dashboard.route
    ) {
        composable(Screen.Dashboard.route) {
            val orderManagementViewModel: OrderManagementViewModel = viewModel(
                factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
            )
            OrderManagementDashboard(
                navController = navController,
                viewModel = orderManagementViewModel
            )
        }

        composable(Screen.Products.route) {
            ProductsScreen(navController = navController)
        }

        composable(Screen.Customers.route) {
            val customersViewModel: CustomersViewModel = viewModel(
                factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
            )
            CustomersScreen(viewModel = customersViewModel)
        }

        composable(Screen.OrderEntry.route) {
            AdvancedOrderEntryScreen(navController = navController)
        }

        composable(Screen.Orders.route) {
            val orderManagementViewModel: OrderManagementViewModel = viewModel(
                factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
            )
            OrdersScreen(
                navController = navController,
                viewModel = orderManagementViewModel
            )
        }

        composable(Screen.Reports.route) {
            val orderManagementViewModel: OrderManagementViewModel = viewModel(
                factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
            )
            ReportsScreen(
                navController = navController,
                viewModel = orderManagementViewModel
            )
        }

        composable("sync_settings") {
            SyncSettingsScreen()
        }
    }
}
