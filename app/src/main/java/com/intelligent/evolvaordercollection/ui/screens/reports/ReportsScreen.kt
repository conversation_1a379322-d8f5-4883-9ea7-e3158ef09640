package com.intelligent.evolvaordercollection.ui.screens.reports

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.intelligent.evolvaordercollection.POSApplication
import com.intelligent.evolvaordercollection.data.model.Order
import com.intelligent.evolvaordercollection.data.model.OrderStatus
import com.intelligent.evolvaordercollection.ui.theme.SuccessColor
import com.intelligent.evolvaordercollection.ui.theme.InfoColor
import com.intelligent.evolvaordercollection.ui.theme.WarningColor
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderManagementViewModel
import com.intelligent.evolvaordercollection.ui.viewmodel.ViewModelFactory
import com.intelligent.evolvaordercollection.utils.CurrencyUtils
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*
import android.content.Context
import android.content.Intent
import android.os.Environment
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileWriter
import android.widget.Toast

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportsScreen(
    navController: NavController,
    viewModel: OrderManagementViewModel = viewModel(
        factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
    )
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    var selectedPeriod by remember { mutableStateOf("Today") }
    var selectedStatus by remember { mutableStateOf("All") }
    var showDataTable by remember { mutableStateOf(true) }
    var sortBy by remember { mutableStateOf("Date") }
    var sortAscending by remember { mutableStateOf(false) }

    // Date range filter states
    var startDate by remember { mutableStateOf<Long?>(null) }
    var endDate by remember { mutableStateOf<Long?>(null) }
    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }

    val periods = listOf("Today", "This Week", "This Month", "This Year", "All Time", "Custom Range")
    val statusOptions = listOf("All", "WAITING", "SUCCESS", "REJECT")
    val sortOptions = listOf("Date", "Amount", "Customer", "Status")

    // Load data when screen is first displayed
    LaunchedEffect(Unit) {
        viewModel.loadDashboardData()
    }

    // Filter orders based on selected criteria
    val filteredOrders = remember(uiState.orders, selectedPeriod, selectedStatus, startDate, endDate) {
        filterOrdersByPeriodAndStatus(uiState.orders, selectedPeriod, selectedStatus, startDate, endDate)
    }

    // Sort orders
    val sortedOrders = remember(filteredOrders, sortBy, sortAscending) {
        sortOrders(filteredOrders, sortBy, sortAscending)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with view toggle
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Reports",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                IconButton(
                    onClick = { showDataTable = false },
                    modifier = Modifier
                        .background(
                            if (!showDataTable) MaterialTheme.colorScheme.primary else Color.Transparent,
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Icon(
                        Icons.Default.Dashboard,
                        contentDescription = "Summary View",
                        tint = if (!showDataTable) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface
                    )
                }
                IconButton(
                    onClick = { showDataTable = true },
                    modifier = Modifier
                        .background(
                            if (showDataTable) MaterialTheme.colorScheme.primary else Color.Transparent,
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Icon(
                        Icons.Default.TableChart,
                        contentDescription = "Table View",
                        tint = if (showDataTable) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Filters Section
        ReportsFiltersSection(
            selectedPeriod = selectedPeriod,
            onPeriodChange = {
                selectedPeriod = it
                if (it == "Custom Range") {
                    showStartDatePicker = true
                }
            },
            selectedStatus = selectedStatus,
            onStatusChange = { selectedStatus = it },
            periods = periods,
            statusOptions = statusOptions,
            sortBy = sortBy,
            onSortByChange = { sortBy = it },
            sortAscending = sortAscending,
            onSortDirectionChange = { sortAscending = it },
            sortOptions = sortOptions,
            showDataTable = showDataTable,
            startDate = startDate,
            endDate = endDate,
            onStartDateClick = { showStartDatePicker = true },
            onEndDateClick = { showEndDatePicker = true },
            onExportClick = { exportToCSV(context, sortedOrders) }
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Content based on view mode
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            if (showDataTable) {
                ReportsDataTable(
                    orders = sortedOrders,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                ReportsSummaryView(
                    orders = filteredOrders,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }

    // Date Pickers
    if (showStartDatePicker) {
        val datePickerState = rememberDatePickerState()
        DatePickerDialog(
            onDateSelected = { selectedDateMillis ->
                startDate = selectedDateMillis
                showStartDatePicker = false
                if (endDate == null) {
                    showEndDatePicker = true
                }
            },
            onDismiss = { showStartDatePicker = false }
        )
    }

    if (showEndDatePicker) {
        val datePickerState = rememberDatePickerState()
        DatePickerDialog(
            onDateSelected = { selectedDateMillis ->
                endDate = selectedDateMillis
                showEndDatePicker = false
            },
            onDismiss = { showEndDatePicker = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportsFiltersSection(
    selectedPeriod: String,
    onPeriodChange: (String) -> Unit,
    selectedStatus: String,
    onStatusChange: (String) -> Unit,
    periods: List<String>,
    statusOptions: List<String>,
    sortBy: String,
    onSortByChange: (String) -> Unit,
    sortAscending: Boolean,
    onSortDirectionChange: (Boolean) -> Unit,
    sortOptions: List<String>,
    showDataTable: Boolean,
    startDate: Long?,
    endDate: Long?,
    onStartDateClick: () -> Unit,
    onEndDateClick: () -> Unit,
    onExportClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // Compact header with filters in rows
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.Top
            ) {
                // Period Filter - Compact
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Period",
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        periods.take(3).forEach { period ->
                            FilterChip(
                                onClick = { onPeriodChange(period) },
                                label = { Text(period, fontSize = 10.sp) },
                                selected = selectedPeriod == period,
                                modifier = Modifier.height(28.dp)
                            )
                        }
                    }
                    if (periods.size > 3) {
                        Spacer(modifier = Modifier.height(2.dp))
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            periods.drop(3).forEach { period ->
                                FilterChip(
                                    onClick = { onPeriodChange(period) },
                                    label = { Text(period, fontSize = 10.sp) },
                                    selected = selectedPeriod == period,
                                    modifier = Modifier.height(28.dp)
                                )
                            }
                        }
                    }
                }

                // Status Filter - Compact
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Status",
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        statusOptions.forEach { status ->
                            FilterChip(
                                onClick = { onStatusChange(status) },
                                label = { Text(status, fontSize = 10.sp) },
                                selected = selectedStatus == status,
                                modifier = Modifier.height(28.dp)
                            )
                        }
                    }
                }
            }

            // Custom Date Range (show when Custom Range is selected)
            if (selectedPeriod == "Custom Range") {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Start Date
                    OutlinedButton(
                        onClick = onStartDateClick,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = if (startDate != null) {
                                SimpleDateFormat("dd-MMM-yyyy", Locale.getDefault()).format(Date(startDate))
                            } else "Start Date",
                            fontSize = 10.sp
                        )
                    }

                    // End Date
                    OutlinedButton(
                        onClick = onEndDateClick,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = if (endDate != null) {
                                SimpleDateFormat("dd-MMM-yyyy", Locale.getDefault()).format(Date(endDate))
                            } else "End Date",
                            fontSize = 10.sp
                        )
                    }
                }
            }

            // Sort Options and Export (only show for data table) - Compact
            if (showDataTable) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Sort:",
                            style = MaterialTheme.typography.labelSmall,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        sortOptions.forEach { option ->
                            FilterChip(
                                onClick = { onSortByChange(option) },
                                label = { Text(option, fontSize = 10.sp) },
                                selected = sortBy == option,
                                modifier = Modifier.height(28.dp)
                            )
                        }
                    }

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Sort Direction Toggle - Compact
                        IconButton(
                            onClick = { onSortDirectionChange(!sortAscending) },
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    MaterialTheme.colorScheme.primaryContainer,
                                    RoundedCornerShape(6.dp)
                                )
                        ) {
                            Icon(
                                if (sortAscending) Icons.Default.ArrowUpward else Icons.Default.ArrowDownward,
                                contentDescription = if (sortAscending) "Ascending" else "Descending",
                                tint = MaterialTheme.colorScheme.onPrimaryContainer,
                                modifier = Modifier.size(16.dp)
                            )
                        }

                        // Export Button
                        IconButton(
                            onClick = onExportClick,
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    MaterialTheme.colorScheme.secondaryContainer,
                                    RoundedCornerShape(6.dp)
                                )
                        ) {
                            Icon(
                                Icons.Default.Download,
                                contentDescription = "Export to CSV",
                                tint = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportCard(report: ReportData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = report.title,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = report.value,
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        color = report.color
                    )
                    if (report.subtitle.isNotBlank()) {
                        Text(
                            text = report.subtitle,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                Icon(
                    imageVector = report.icon,
                    contentDescription = report.title,
                    tint = report.color,
                    modifier = Modifier.size(32.dp)
                )
            }
        }
    }
}

@Composable
fun ReportsDataTable(
    orders: List<Order>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Orders Data (${orders.size} records)",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                IconButton(
                    onClick = { /* TODO: Export functionality */ }
                ) {
                    Icon(
                        Icons.Default.FileDownload,
                        contentDescription = "Export Data",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            if (orders.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.TableChart,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "No data available",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {
                // Data Table - Fixed Layout
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(0.dp)
                ) {
                    // Table Header
                    item {
                        DataTableHeader()
                        Divider(
                            color = MaterialTheme.colorScheme.outline,
                            thickness = 1.dp
                        )
                    }

                    // Table Rows
                    items(orders) { order ->
                        DataTableRow(order = order)
                        Divider(
                            color = MaterialTheme.colorScheme.outlineVariant,
                            thickness = 0.5.dp
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun DataTableHeader() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surfaceVariant)
            .padding(vertical = 8.dp, horizontal = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "Order #",
            modifier = Modifier.weight(1.2f),
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = "Date",
            modifier = Modifier.weight(1.0f),
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = "Customer",
            modifier = Modifier.weight(1.5f),
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = "Status",
            modifier = Modifier.weight(1.0f),
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = "Amount",
            modifier = Modifier.weight(1.0f),
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.End
        )
        Text(
            text = "Notes",
            modifier = Modifier.weight(1.5f),
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun DataTableRow(order: Order) {
    val dateFormat = remember { SimpleDateFormat("dd-MMM-yyyy", Locale.getDefault()) }
    val timeFormat = remember { SimpleDateFormat("HH:mm", Locale.getDefault()) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { /* TODO: Show order details */ }
            .padding(vertical = 8.dp, horizontal = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Order Number
        Text(
            text = order.orderNumber,
            modifier = Modifier.weight(1.2f),
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        // Date
        Column(
            modifier = Modifier.weight(1.0f)
        ) {
            Text(
                text = dateFormat.format(Date(order.createdAt)),
                style = MaterialTheme.typography.bodySmall,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = timeFormat.format(Date(order.createdAt)),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontSize = 10.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // Customer
        Text(
            text = order.customerName.ifEmpty { "Walk In" },
            modifier = Modifier.weight(1.5f),
            style = MaterialTheme.typography.bodySmall,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        // Status
        Box(
            modifier = Modifier.weight(1.0f),
            contentAlignment = Alignment.CenterStart
        ) {
            StatusChip(status = order.status)
        }

        // Amount
        Text(
            text = CurrencyUtils.formatPrice(order.totalAmount),
            modifier = Modifier.weight(1.0f),
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.End,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        // Notes
        Text(
            text = order.notes.ifEmpty { "-" },
            modifier = Modifier.weight(1.5f),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

@Composable
fun StatusChip(
    status: OrderStatus
) {
    val (backgroundColor, textColor, text) = when (status) {
        OrderStatus.WAITING -> Triple(
            WarningColor.copy(alpha = 0.15f),
            WarningColor,
            "WAIT"
        )
        OrderStatus.SUCCESS -> Triple(
            SuccessColor.copy(alpha = 0.15f),
            SuccessColor,
            "DONE"
        )
        OrderStatus.REJECT -> Triple(
            Color.Red.copy(alpha = 0.15f),
            Color.Red,
            "REJECT"
        )
    }

    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .padding(horizontal = 6.dp, vertical = 2.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            fontSize = 9.sp,
            color = textColor,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun ReportsSummaryView(
    orders: List<Order>,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(getReportCards(orders)) { report ->
            ReportCard(report = report)
        }
    }
}

data class ReportData(
    val title: String,
    val value: String,
    val subtitle: String = "",
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val color: androidx.compose.ui.graphics.Color
)

// Utility functions for filtering and sorting
fun filterOrdersByPeriodAndStatus(
    orders: List<Order>,
    period: String,
    status: String,
    startDate: Long? = null,
    endDate: Long? = null
): List<Order> {
    val now = System.currentTimeMillis()
    val calendar = Calendar.getInstance()

    // Filter by period
    val periodFilteredOrders = when (period) {
        "Today" -> {
            calendar.timeInMillis = now
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            orders.filter { it.createdAt >= startOfDay }
        }
        "This Week" -> {
            calendar.timeInMillis = now
            calendar.set(Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            orders.filter { it.createdAt >= startOfWeek }
        }
        "This Month" -> {
            calendar.timeInMillis = now
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            orders.filter { it.createdAt >= startOfMonth }
        }
        "This Year" -> {
            calendar.timeInMillis = now
            calendar.set(Calendar.DAY_OF_YEAR, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfYear = calendar.timeInMillis
            orders.filter { it.createdAt >= startOfYear }
        }
        "Custom Range" -> {
            if (startDate != null && endDate != null) {
                orders.filter { it.createdAt >= startDate && it.createdAt <= endDate + (24 * 60 * 60 * 1000 - 1) }
            } else if (startDate != null) {
                orders.filter { it.createdAt >= startDate }
            } else if (endDate != null) {
                orders.filter { it.createdAt <= endDate + (24 * 60 * 60 * 1000 - 1) }
            } else {
                orders
            }
        }
        else -> orders // "All Time"
    }

    // Filter by status
    return if (status == "All") {
        periodFilteredOrders
    } else {
        val orderStatus = try {
            OrderStatus.valueOf(status)
        } catch (e: Exception) {
            return periodFilteredOrders
        }
        periodFilteredOrders.filter { it.status == orderStatus }
    }
}

fun sortOrders(
    orders: List<Order>,
    sortBy: String,
    ascending: Boolean
): List<Order> {
    val sorted = when (sortBy) {
        "Date" -> orders.sortedBy { it.createdAt }
        "Amount" -> orders.sortedBy { it.totalAmount }
        "Customer" -> orders.sortedBy { it.customerName.ifEmpty { "Walk In Customer" } }
        "Status" -> orders.sortedBy { it.status.name }
        else -> orders
    }

    return if (ascending) sorted else sorted.reversed()
}

fun getReportCards(orders: List<Order>): List<ReportData> {
    val totalSales = orders.filter { it.status == OrderStatus.SUCCESS }.sumOf { it.totalAmount }
    val totalOrders = orders.size
    val waitingOrders = orders.count { it.status == OrderStatus.WAITING }
    val successOrders = orders.count { it.status == OrderStatus.SUCCESS }
    val rejectedOrders = orders.count { it.status == OrderStatus.REJECT }
    val averageOrderValue = if (successOrders > 0) totalSales.divide(BigDecimal(successOrders), 2, java.math.RoundingMode.HALF_UP) else BigDecimal.ZERO

    // Calculate additional metrics
    val successRate = if (totalOrders > 0) (successOrders.toDouble() / totalOrders * 100).toInt() else 0
    val topCustomers = orders.groupBy { it.customerName.ifEmpty { "Walk In Customer" } }
        .mapValues { it.value.size }
        .toList()
        .sortedByDescending { it.second }
        .take(3)

    val recentOrdersCount = orders.filter {
        it.createdAt >= System.currentTimeMillis() - (24 * 60 * 60 * 1000) // Last 24 hours
    }.size

    return listOf(
        ReportData(
            title = "Total Sales",
            value = CurrencyUtils.formatPrice(totalSales),
            subtitle = "$successOrders successful transactions",
            icon = Icons.Default.TrendingUp,
            color = SuccessColor
        ),
        ReportData(
            title = "Total Orders",
            value = totalOrders.toString(),
            subtitle = "$waitingOrders waiting, $successOrders success, $rejectedOrders rejected",
            icon = Icons.Default.ShoppingCart,
            color = InfoColor
        ),
        ReportData(
            title = "Average Order Value",
            value = CurrencyUtils.formatPrice(averageOrderValue),
            subtitle = if (successOrders > 0) "Based on $successOrders successful orders" else "No successful orders yet",
            icon = Icons.Default.Analytics,
            color = SuccessColor
        ),
        ReportData(
            title = "Success Rate",
            value = "$successRate%",
            subtitle = "Order completion rate",
            icon = Icons.Default.CheckCircle,
            color = when {
                successRate >= 80 -> SuccessColor
                successRate >= 60 -> WarningColor
                else -> Color.Red
            }
        ),
        ReportData(
            title = "Recent Activity",
            value = "$recentOrdersCount orders",
            subtitle = "In the last 24 hours",
            icon = Icons.Default.Schedule,
            color = InfoColor
        ),
        ReportData(
            title = "Top Customer",
            value = topCustomers.firstOrNull()?.first ?: "No orders",
            subtitle = if (topCustomers.isNotEmpty()) "${topCustomers.first().second} orders" else "No data available",
            icon = Icons.Default.Person,
            color = InfoColor
        )
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerDialog(
    onDateSelected: (Long?) -> Unit,
    onDismiss: () -> Unit
) {
    val datePickerState = rememberDatePickerState()

    DatePickerDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            TextButton(onClick = { onDateSelected(datePickerState.selectedDateMillis) }) {
                Text("OK")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    ) {
        DatePicker(state = datePickerState)
    }
}

fun exportToCSV(context: Context, orders: List<Order>) {
    try {
        val fileName = "reports_${SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())}.csv"
        val file = File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), fileName)

        val csvContent = StringBuilder()
        csvContent.append("Order Number,Date,Time,Customer,Status,Amount,Notes\n")

        val dateFormat = SimpleDateFormat("dd-MMM-yyyy", Locale.getDefault())
        val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())

        orders.forEach { order ->
            val date = dateFormat.format(Date(order.createdAt))
            val time = timeFormat.format(Date(order.createdAt))
            val customer = order.customerName.ifEmpty { "Walk In Customer" }
            val amount = CurrencyUtils.formatPrice(order.totalAmount).replace(",", "")
            val notes = order.notes.replace(",", ";").replace("\n", " ")

            csvContent.append("${order.orderNumber},$date,$time,$customer,${order.status},$amount,\"$notes\"\n")
        }

        FileWriter(file).use { writer ->
            writer.write(csvContent.toString())
        }

        // Share the file
        val uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )

        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/csv"
            putExtra(Intent.EXTRA_STREAM, uri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        context.startActivity(Intent.createChooser(shareIntent, "Export Reports"))

        Toast.makeText(context, "Reports exported successfully", Toast.LENGTH_SHORT).show()

    } catch (e: Exception) {
        Toast.makeText(context, "Export failed: ${e.message}", Toast.LENGTH_LONG).show()
    }
}
