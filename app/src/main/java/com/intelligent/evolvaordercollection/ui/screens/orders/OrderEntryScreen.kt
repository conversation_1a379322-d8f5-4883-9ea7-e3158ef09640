package com.intelligent.evolvaordercollection.ui.screens.orders

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import android.content.Intent
import android.widget.Toast
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import coil.compose.AsyncImage
import com.intelligent.evolvaordercollection.POSApplication
import com.intelligent.evolvaordercollection.data.model.CartItem
import com.intelligent.evolvaordercollection.data.model.Customer
import com.intelligent.evolvaordercollection.data.model.Product
import com.intelligent.evolvaordercollection.ui.components.BarcodeScannerDialog
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderCollectionViewModel
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderCollectionUiState
import com.intelligent.evolvaordercollection.ui.viewmodel.ProductsViewModel
import com.intelligent.evolvaordercollection.ui.viewmodel.ProductsUiState
import com.intelligent.evolvaordercollection.ui.viewmodel.ViewModelFactory
import com.intelligent.evolvaordercollection.utils.CurrencyUtils
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderEntryScreen(
    navController: NavController,
    orderViewModel: OrderCollectionViewModel = viewModel(
        factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
    ),
    productsViewModel: ProductsViewModel = viewModel(
        factory = ViewModelFactory((LocalContext.current.applicationContext as POSApplication).container)
    )
) {
    val orderUiState by orderViewModel.uiState.collectAsState()
    val productsUiState by productsViewModel.uiState.collectAsState()
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
    val isTablet = configuration.screenWidthDp >= 600
    
    // Determine layout based on screen size
    if (isTablet || isLandscape) {
        // Split-pane layout for tablets and landscape
        SplitPaneOrderEntry(
            orderUiState = orderUiState,
            productsUiState = productsUiState,
            orderViewModel = orderViewModel,
            productsViewModel = productsViewModel,
            navController = navController
        )
    } else {
        // Single pane layout for phones in portrait
        SinglePaneOrderEntry(
            orderUiState = orderUiState,
            productsUiState = productsUiState,
            orderViewModel = orderViewModel,
            productsViewModel = productsViewModel,
            navController = navController
        )
    }
}

@Composable
fun SplitPaneOrderEntry(
    orderUiState: OrderCollectionUiState,
    productsUiState: ProductsUiState,
    orderViewModel: OrderCollectionViewModel,
    productsViewModel: ProductsViewModel,
    navController: NavController
) {
    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Left Pane - Invoice View
        Card(
            modifier = Modifier
                .weight(0.4f)
                .fillMaxHeight(),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            InvoiceView(
                orderUiState = orderUiState,
                orderViewModel = orderViewModel,
                modifier = Modifier.fillMaxSize()
            )
        }

        // Right Pane - Products List View
        Card(
            modifier = Modifier
                .weight(0.6f)
                .fillMaxHeight(),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            ProductsListView(
                productsUiState = productsUiState,
                productsViewModel = productsViewModel,
                orderViewModel = orderViewModel,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
    
    // Customer Selection Dialog
    if (orderUiState.showCustomerDialog) {
        CustomerSelectionDialog(
            customers = orderUiState.customers,
            onCustomerSelected = { customer ->
                orderViewModel.selectCustomer(customer)
            },
            onDismiss = { orderViewModel.hideCustomerDialog() }
        )
    }
}

@Composable
fun SinglePaneOrderEntry(
    orderUiState: OrderCollectionUiState,
    productsUiState: ProductsUiState,
    orderViewModel: OrderCollectionViewModel,
    productsViewModel: ProductsViewModel,
    navController: NavController
) {
    var showInvoice by remember { mutableStateOf(true) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Toggle buttons for single pane
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = { showInvoice = true },
                modifier = Modifier.weight(1f),
                colors = if (showInvoice) ButtonDefaults.buttonColors() 
                        else ButtonDefaults.outlinedButtonColors()
            ) {
                Icon(Icons.Default.Receipt, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Invoice")
            }
            
            Button(
                onClick = { showInvoice = false },
                modifier = Modifier.weight(1f),
                colors = if (!showInvoice) ButtonDefaults.buttonColors() 
                        else ButtonDefaults.outlinedButtonColors()
            ) {
                Icon(Icons.Default.Inventory, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Products")
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Content based on selection
        if (showInvoice) {
            InvoiceView(
                orderUiState = orderUiState,
                orderViewModel = orderViewModel,
                modifier = Modifier.fillMaxSize()
            )
        } else {
            ProductsListView(
                productsUiState = productsUiState,
                productsViewModel = productsViewModel,
                orderViewModel = orderViewModel,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
    
    // Customer Selection Dialog
    if (orderUiState.showCustomerDialog) {
        CustomerSelectionDialog(
            customers = orderUiState.customers,
            onCustomerSelected = { customer ->
                orderViewModel.selectCustomer(customer)
            },
            onDismiss = { orderViewModel.hideCustomerDialog() }
        )
    }
}

@Composable
fun InvoiceView(
    orderUiState: OrderCollectionUiState,
    orderViewModel: OrderCollectionViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    Column(
        modifier = modifier.padding(20.dp)
    ) {
        // Beautiful Header with gradient background
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Removed large Invoice title for compact UI

                Row {
                    IconButton(
                        onClick = { orderViewModel.clearCart() },
                        colors = IconButtonDefaults.iconButtonColors(
                            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.7f)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "Clear Cart",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    IconButton(
                        onClick = { orderViewModel.showCustomerDialog() },
                        colors = IconButtonDefaults.iconButtonColors(
                            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.7f)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "Select Customer",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    IconButton(
                        onClick = { orderViewModel.syncCustomers() },
                        colors = IconButtonDefaults.iconButtonColors(
                            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.7f)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Sync,
                            contentDescription = "Sync Customers",
                            tint = MaterialTheme.colorScheme.secondary
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Customer Info
        if (orderUiState.selectedCustomer != null) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.secondaryContainer
                ),
                shape = RoundedCornerShape(12.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Surface(
                        color = MaterialTheme.colorScheme.secondary,
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSecondary,
                            modifier = Modifier.padding(8.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                    Column {
                        Text(
                            text = orderUiState.selectedCustomer!!.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSecondaryContainer
                        )
                        Text(
                            text = orderUiState.selectedCustomer!!.phone,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = 0.7f)
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(20.dp))
        }
        
        // Cart Items
        if (orderUiState.cartItems.isNotEmpty()) {
            Text(
                text = "Order Items (${orderUiState.cartItems.size})",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(4.dp))

            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                items(orderUiState.cartItems) { cartItem ->
                    InvoiceItemCard(
                        cartItem = cartItem,
                        onQuantityChange = { newQuantity ->
                            orderViewModel.updateCartItemQuantity(cartItem.product.id, newQuantity)
                        },
                        onRemove = {
                            orderViewModel.removeFromCart(cartItem.product.id)
                        },
                        onDiscountChange = { newDiscount ->
                            orderViewModel.updateCartItemDiscount(cartItem.product.id, newDiscount)
                        },
                        isCompact = true // Always use compact mode in OrderEntryScreen
                    )
                }
            }
        } else {
            // Empty state
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.ShoppingCart,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "No items in cart",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // Order Summary (always visible when items exist)
        if (orderUiState.cartItems.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.tertiaryContainer
                ),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Receipt,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onTertiaryContainer,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Order Summary",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onTertiaryContainer
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Subtotal:",
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Text(
                            text = CurrencyUtils.formatPrice(orderUiState.subtotal),
                            style = MaterialTheme.typography.bodyLarge,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    if (orderUiState.discount > java.math.BigDecimal.ZERO) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Discount:",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = "-${CurrencyUtils.formatPrice(orderUiState.discount)}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }

                    Divider(modifier = Modifier.padding(vertical = 12.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Total:",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = CurrencyUtils.formatPrice(orderUiState.total),
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Remark Field
            OutlinedTextField(
                value = orderUiState.remark,
                onValueChange = { orderViewModel.updateRemark(it) },
                modifier = Modifier.fillMaxWidth(),
                label = { Text("Remark (Optional)") },
                placeholder = { Text("Add any notes for this order...") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Remark"
                    )
                },
                maxLines = 3,
                shape = RoundedCornerShape(12.dp)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Action Buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = { orderViewModel.saveAsDraft() },
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(12.dp),
                    contentPadding = PaddingValues(vertical = 12.dp)
                ) {
                    Icon(Icons.Default.Save, contentDescription = null)
                    Spacer(modifier = Modifier.width(6.dp))
                    Text("Save Draft")
                }

                Button(
                    onClick = { orderViewModel.createOrder() },
                    modifier = Modifier.weight(1f),
                    enabled = orderUiState.selectedCustomer != null,
                    shape = RoundedCornerShape(12.dp),
                    contentPadding = PaddingValues(vertical = 12.dp)
                ) {
                    Icon(Icons.Default.ShoppingCart, contentDescription = null)
                    Spacer(modifier = Modifier.width(6.dp))
                    Text("Create Order")
                }

                // Calculator Button
                IconButton(
                    onClick = {
                        // Try multiple methods to open calculator
                        val calculatorPackages = listOf(
                            "com.android.calculator2",
                            "com.google.android.calculator",
                            "com.samsung.android.calculator",
                            "com.miui.calculator",
                            "com.oneplus.calculator",
                            "com.huawei.calculator",
                            "com.coloros.calculator",
                            "com.oppo.calculator",
                            "com.honor.calculator",
                            "com.hihonor.calculator",
                            "com.huawei.android.calculator",
                            "com.honor.android.calculator",
                            "com.hihonor.android.calculator",
                            "calculator",
                            "com.sec.android.app.popupcalculator",
                            "com.android.calculator",
                            "com.calculator",
                            "calculator.android",
                            "com.magicOS.calculator"
                        )

                        var launched = false

                        // Method 1: Try category-based intent
                        try {
                            val intent = Intent(Intent.ACTION_MAIN).apply {
                                addCategory(Intent.CATEGORY_APP_CALCULATOR)
                                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            }
                            context.startActivity(intent)
                            launched = true
                        } catch (e: Exception) {
                            // Continue to next method
                        }

                        // Method 2: Try specific calculator packages
                        if (!launched) {
                            for (packageName in calculatorPackages) {
                                try {
                                    val intent = context.packageManager.getLaunchIntentForPackage(packageName)
                                    if (intent != null) {
                                        context.startActivity(intent)
                                        launched = true
                                        break
                                    }
                                } catch (e: Exception) {
                                    // Continue to next package
                                }
                            }
                        }

                        // Method 3: Try to find any app with "calculator" in the name or package
                        if (!launched) {
                            try {
                                val pm = context.packageManager
                                val apps = pm.getInstalledApplications(0)
                                val foundCalculators = mutableListOf<String>()

                                for (app in apps) {
                                    val appName = pm.getApplicationLabel(app).toString().lowercase()
                                    val packageName = app.packageName.lowercase()

                                    if (appName.contains("calculator") || appName.contains("calc") ||
                                        packageName.contains("calculator") || packageName.contains("calc")) {
                                        foundCalculators.add("${app.packageName} - $appName")
                                        val intent = pm.getLaunchIntentForPackage(app.packageName)
                                        if (intent != null) {
                                            context.startActivity(intent)
                                            launched = true
                                            break
                                        }
                                    }
                                }

                                // Debug: Show found calculators if none launched
                                if (!launched && foundCalculators.isNotEmpty()) {
                                    Toast.makeText(context, "Found calculators but couldn't launch: ${foundCalculators.joinToString(", ")}", Toast.LENGTH_LONG).show()
                                }
                            } catch (e: Exception) {
                                Toast.makeText(context, "Error searching for calculator: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        }

                        // Method 4: Try universal calculator intent
                        if (!launched) {
                            try {
                                val intent = Intent("android.intent.action.CALCULATOR")
                                context.startActivity(intent)
                                launched = true
                            } catch (e: Exception) {
                                // Continue
                            }
                        }

                        // If still not launched, show a message
                        if (!launched) {
                            Toast.makeText(context, "Calculator app not found on this device", Toast.LENGTH_SHORT).show()
                        }
                    },
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            MaterialTheme.colorScheme.secondaryContainer,
                            RoundedCornerShape(12.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Calculate,
                        contentDescription = "Calculator",
                        tint = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }
            }
        }
    }
}

@Composable
fun ProductsListView(
    productsUiState: ProductsUiState,
    productsViewModel: ProductsViewModel,
    orderViewModel: OrderCollectionViewModel,
    modifier: Modifier = Modifier
) {
    val orderUiState by orderViewModel.uiState.collectAsState()
    var showSearch by remember { mutableStateOf(false) }
    var showBarcodeScanner by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()

    Column(
        modifier = modifier.padding(16.dp)
    ) {
        // Compact header with search and barcode scanner
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Barcode Scanner Button
            IconButton(onClick = { showBarcodeScanner = true }) {
                Icon(
                    imageVector = Icons.Default.QrCode,
                    contentDescription = "Scan Barcode"
                )
            }

            // Search Button
            IconButton(onClick = { showSearch = !showSearch }) {
                Icon(
                    imageVector = if (showSearch) Icons.Default.Close else Icons.Default.Search,
                    contentDescription = if (showSearch) "Close Search" else "Search Products"
                )
            }
        }

        // Search Bar
        if (showSearch) {
            Spacer(modifier = Modifier.height(8.dp))
            OutlinedTextField(
                value = orderUiState.searchQuery,
                onValueChange = { orderViewModel.updateSearchQuery(it) },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("Search products...") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "Search"
                    )
                },
                trailingIcon = {
                    if (orderUiState.searchQuery.isNotEmpty()) {
                        IconButton(
                            onClick = { orderViewModel.updateSearchQuery("") }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Clear search"
                            )
                        }
                    }
                },
                singleLine = true
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Category filter chips
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(bottom = 16.dp)
        ) {
            items(productsUiState.categories) { category ->
                FilterChip(
                    onClick = { productsViewModel.filterByCategory(category) },
                    label = { Text(category) },
                    selected = productsUiState.selectedCategory == category
                )
            }
        }

        // Products grid
        if (productsUiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // Filter products based on search query and category
            val filteredProducts = remember(productsUiState.filteredProducts, orderUiState.searchQuery) {
                if (orderUiState.searchQuery.isBlank()) {
                    productsUiState.filteredProducts
                } else {
                    productsUiState.filteredProducts.filter { product ->
                        product.name.contains(orderUiState.searchQuery, ignoreCase = true) ||
                        product.description.contains(orderUiState.searchQuery, ignoreCase = true) ||
                        product.category.contains(orderUiState.searchQuery, ignoreCase = true)
                    }
                }
            }

            if (filteredProducts.isEmpty() && orderUiState.searchQuery.isNotBlank()) {
                // No search results
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "No products found",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "Try a different search term",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {
                LazyVerticalGrid(
                    columns = GridCells.Adaptive(minSize = 140.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(bottom = 16.dp)
                ) {
                    items(filteredProducts) { product ->
                        ProductGridCard(
                            product = product,
                            cartQuantity = orderViewModel.getCartQuantity(product.id),
                            onProductClick = { orderViewModel.addToCart(it) }
                        )
                    }
                }
            }
        }
    }

    // Barcode Scanner Dialog
    if (showBarcodeScanner) {
        BarcodeScannerDialog(
            onBarcodeScanned = { scannedCode ->
                // Search for product by barcode or product code
                scope.launch {
                    val productByBarcode = productsViewModel.getProductByBarcode(scannedCode)
                    val productByCode = productsViewModel.getProductByProductCode(scannedCode)

                    val foundProduct = productByBarcode ?: productByCode
                    foundProduct?.let { product ->
                        if (product.stockQuantity > 0) {
                            orderViewModel.addToCart(product)
                        }
                    }
                }
                showBarcodeScanner = false
            },
            onDismiss = { showBarcodeScanner = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductGridCard(
    product: Product,
    cartQuantity: Int = 0,
    onProductClick: (Product) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = if (cartQuantity > 0) 6.dp else 2.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (cartQuantity > 0)
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            else
                MaterialTheme.colorScheme.surface
        ),
        onClick = {
            if (product.stockQuantity > 0) {
                onProductClick(product)
            }
        }
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Product Image
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(100.dp)
                    .clip(RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp))
            ) {
                AsyncImage(
                    model = getProductImageUrl(product.category),
                    contentDescription = product.name,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )

                // Quantity indicator (top-left)
                if (cartQuantity > 0) {
                    Surface(
                        modifier = Modifier
                            .padding(4.dp)
                            .align(Alignment.TopStart),
                        color = MaterialTheme.colorScheme.primary,
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = cartQuantity.toString(),
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onPrimary,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                // Stock indicator (centered)
                if (product.stockQuantity <= 10) {
                    Surface(
                        modifier = Modifier
                            .align(Alignment.Center),
                        color = if (product.stockQuantity == 0) MaterialTheme.colorScheme.error
                               else MaterialTheme.colorScheme.tertiary,
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = if (product.stockQuantity == 0) "Out of Stock" else "Low Stock",
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                            style = MaterialTheme.typography.labelSmall,
                            color = if (product.stockQuantity == 0) MaterialTheme.colorScheme.onError
                                   else MaterialTheme.colorScheme.onTertiary,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }

            // Product Details
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp)
            ) {
                Text(
                    text = product.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = CurrencyUtils.formatPrice(product.price),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.weight(1f))

                // Clean stock and cart info
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Stock: ${product.stockQuantity}",
                        style = MaterialTheme.typography.labelSmall,
                        color = if (product.stockQuantity > 0)
                            MaterialTheme.colorScheme.onSurfaceVariant
                        else
                            MaterialTheme.colorScheme.error,
                        fontWeight = if (product.stockQuantity <= 10) FontWeight.Bold else FontWeight.Normal
                    )

                    if (cartQuantity > 0) {
                        Surface(
                            color = MaterialTheme.colorScheme.primary,
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Text(
                                text = "×$cartQuantity",
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp),
                                style = MaterialTheme.typography.labelMedium,
                                color = MaterialTheme.colorScheme.onPrimary,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
        }
    }
}
