package com.intelligent.evolvaordercollection.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.intelligent.evolvaordercollection.data.model.*
import com.intelligent.evolvaordercollection.data.repository.OrderRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.util.*

class OrderManagementViewModel(
    private val orderRepository: OrderRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(OrderManagementUiState())
    val uiState: StateFlow<OrderManagementUiState> = _uiState.asStateFlow()

    private val _selectedOrder = MutableStateFlow<Order?>(null)
    val selectedOrder: StateFlow<Order?> = _selectedOrder.asStateFlow()

    private val _alerts = MutableStateFlow<List<OrderAlert>>(emptyList())
    val alerts: StateFlow<List<OrderAlert>> = _alerts.asStateFlow()

    private val _performanceMetrics = MutableStateFlow(PerformanceMetrics())
    val performanceMetrics: StateFlow<PerformanceMetrics> = _performanceMetrics.asStateFlow()

    init {
        // Load data in sequence to ensure proper initialization
        loadPerformanceMetrics()
        loadAlerts()
        loadDashboardData()

        // Observe orders continuously for real-time updates
        observeOrders()
    }

    private fun observeOrders() {
        viewModelScope.launch {
            orderRepository.getAllOrders().collect { orders ->
                // Update UI state with real orders or demo data if empty
                val ordersToShow = if (orders.isEmpty()) getDemoOrders() else orders

                // Apply current filters
                val currentFilter = _uiState.value.currentFilter
                val filteredOrders = applyFilters(ordersToShow, currentFilter)

                // Sort orders
                val sortedOrders = sortOrders(filteredOrders, _uiState.value.sortBy, _uiState.value.sortDirection)

                _uiState.value = _uiState.value.copy(
                    orders = sortedOrders,
                    totalOrders = sortedOrders.size,
                    isLoading = false,
                    error = null
                )

                // Also update performance metrics when orders change
                updatePerformanceMetricsFromOrders(ordersToShow)
            }
        }
    }

    fun loadDashboardData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                // Load orders with current filters
                val currentFilter = _uiState.value.currentFilter
                var orders = when {
                    currentFilter.status.isNotEmpty() -> {
                        // Filter by status
                        orderRepository.getAllOrders().first().filter { order ->
                            currentFilter.status.contains(order.status)
                        }
                    }
                    else -> orderRepository.getAllOrders().first()
                }

                // If no orders exist, provide demo data
                if (orders.isEmpty()) {
                    orders = getDemoOrders()
                }

                // Apply additional filters
                val filteredOrders = applyFilters(orders, currentFilter)

                // Sort orders
                val sortedOrders = sortOrders(filteredOrders, _uiState.value.sortBy, _uiState.value.sortDirection)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    orders = sortedOrders,
                    totalOrders = sortedOrders.size,
                    error = null
                )

            } catch (e: Exception) {
                // Provide demo data if there's an error
                val demoOrders = getDemoOrders()
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    orders = demoOrders,
                    totalOrders = demoOrders.size,
                    error = null // Don't show error, just use demo data
                )
            }
        }
    }

    fun applyFilter(filter: OrderFilter) {
        _uiState.value = _uiState.value.copy(currentFilter = filter)
        loadDashboardData()
    }

    fun updateSearchQuery(query: String) {
        val currentFilter = _uiState.value.currentFilter.copy(searchQuery = query)
        applyFilter(currentFilter)
    }

    fun sortOrders(sortBy: OrderSortBy, direction: SortDirection) {
        _uiState.value = _uiState.value.copy(
            sortBy = sortBy,
            sortDirection = direction
        )
        loadDashboardData()
    }

    fun selectOrder(order: Order) {
        _selectedOrder.value = order
        _uiState.value = _uiState.value.copy(
            selectedOrderDetails = order,
            showOrderDetailsDialog = true
        )
        loadOrderDetails(order.id)
    }

    fun hideOrderDetailsDialog() {
        _uiState.value = _uiState.value.copy(
            showOrderDetailsDialog = false,
            selectedOrderDetails = null,
            selectedOrderItems = emptyList()
        )
        _selectedOrder.value = null
    }

    fun updateOrderStatus(orderId: Long, newStatus: OrderStatus) {
        viewModelScope.launch {
            try {
                orderRepository.updateOrderStatus(orderId, newStatus)
                loadDashboardData()
                
                // Create tracking entry
                createTrackingEntry(orderId, newStatus)
                
                // Generate alert if needed
                generateStatusChangeAlert(orderId, newStatus)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = e.message)
            }
        }
    }

    fun assignOrderToAgent(orderId: Long, agentId: Long, collectionPointId: Long? = null) {
        viewModelScope.launch {
            try {
                // Create assignment record
                val assignment = OrderAssignment(
                    orderId = orderId,
                    agentId = agentId,
                    collectionPointId = collectionPointId,
                    estimatedCollectionTime = System.currentTimeMillis() + (60 * 60 * 1000) // 1 hour from now
                )
                
                // Update order status to waiting (since OUT_FOR_DELIVERY no longer exists)
                updateOrderStatus(orderId, OrderStatus.WAITING)
                
                // Generate assignment alert
                generateAssignmentAlert(orderId, agentId)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = e.message)
            }
        }
    }

    fun toggleWidget(widgetId: String) {
        val currentWidgets = _uiState.value.dashboardWidgets.toMutableList()
        val widgetIndex = currentWidgets.indexOfFirst { it.id == widgetId }
        
        if (widgetIndex >= 0) {
            val widget = currentWidgets[widgetIndex]
            currentWidgets[widgetIndex] = widget.copy(isVisible = !widget.isVisible)
            _uiState.value = _uiState.value.copy(dashboardWidgets = currentWidgets)
        }
    }

    fun reorderWidgets(fromIndex: Int, toIndex: Int) {
        val currentWidgets = _uiState.value.dashboardWidgets.toMutableList()
        val widget = currentWidgets.removeAt(fromIndex)
        currentWidgets.add(toIndex, widget.copy(position = toIndex))
        
        // Update positions for all widgets
        currentWidgets.forEachIndexed { index, w ->
            currentWidgets[index] = w.copy(position = index)
        }
        
        _uiState.value = _uiState.value.copy(dashboardWidgets = currentWidgets)
    }

    fun markAlertAsRead(alertId: Long) {
        viewModelScope.launch {
            val currentAlerts = _alerts.value.toMutableList()
            val alertIndex = currentAlerts.indexOfFirst { it.id == alertId }
            
            if (alertIndex >= 0) {
                currentAlerts[alertIndex] = currentAlerts[alertIndex].copy(isRead = true)
                _alerts.value = currentAlerts
            }
        }
    }

    fun exportData(config: ExportConfig) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isExporting = true)
                
                // Get filtered data based on export config
                val dataToExport = getExportData(config)
                
                // Generate export file based on format
                when (config.format) {
                    ExportFormat.CSV -> generateCSVExport(dataToExport, config)
                    ExportFormat.EXCEL -> generateExcelExport(dataToExport, config)
                    ExportFormat.PDF -> generatePDFExport(dataToExport, config)
                    ExportFormat.JSON -> generateJSONExport(dataToExport, config)
                }
                
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    exportCompleted = true
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    error = e.message
                )
            }
        }
    }

    private fun applyFilters(orders: List<Order>, filter: OrderFilter): List<Order> {
        return orders.filter { order ->
            // Status filter
            (filter.status.isEmpty() || filter.status.contains(order.status)) &&
            
            // Date range filter
            (filter.dateRange == null || 
             (order.createdAt >= filter.dateRange.startDate && 
              order.createdAt <= filter.dateRange.endDate)) &&
            
            // Customer filter
            (filter.customerId == null || order.customerId == filter.customerId) &&
            
            // Amount range filter
            (filter.minAmount == null || order.totalAmount >= filter.minAmount) &&
            (filter.maxAmount == null || order.totalAmount <= filter.maxAmount) &&
            
            // Search query filter
            (filter.searchQuery.isEmpty() || 
             order.orderNumber.contains(filter.searchQuery, ignoreCase = true) ||
             order.customerName.contains(filter.searchQuery, ignoreCase = true))
        }
    }

    private fun sortOrders(orders: List<Order>, sortBy: OrderSortBy, direction: SortDirection): List<Order> {
        val sorted = when (sortBy) {
            OrderSortBy.ORDER_NUMBER -> orders.sortedBy { it.orderNumber }
            OrderSortBy.CUSTOMER_NAME -> orders.sortedBy { it.customerName }
            OrderSortBy.STATUS -> orders.sortedBy { it.status.name }
            OrderSortBy.TOTAL_AMOUNT -> orders.sortedBy { it.totalAmount }
            OrderSortBy.CREATED_DATE -> orders.sortedBy { it.createdAt }
            OrderSortBy.UPDATED_DATE -> orders.sortedBy { it.updatedAt }
        }
        
        return if (direction == SortDirection.DESCENDING) sorted.reversed() else sorted
    }

    private fun loadOrderDetails(orderId: Long) {
        viewModelScope.launch {
            try {
                val orderDetails = orderRepository.getOrderById(orderId)
                val orderItems = orderRepository.getOrderItems(orderId).first()

                _uiState.value = _uiState.value.copy(
                    selectedOrderDetails = orderDetails,
                    selectedOrderItems = orderItems
                )
            } catch (e: Exception) {
                // Provide demo order items if repository fails
                val demoOrderItems = getDemoOrderItems(orderId)
                _uiState.value = _uiState.value.copy(
                    selectedOrderItems = demoOrderItems
                )
            }
        }
    }

    private fun getDemoOrderItems(orderId: Long): List<OrderItem> {
        return when (orderId) {
            1L -> listOf(
                OrderItem(
                    id = 1,
                    orderId = orderId,
                    productId = 1,
                    productName = "Premium Coffee",
                    quantity = 2,
                    unitPrice = BigDecimal("2250"),
                    totalPrice = BigDecimal("4500")
                )
            )
            2L -> listOf(
                OrderItem(
                    id = 2,
                    orderId = orderId,
                    productId = 2,
                    productName = "Organic Tea",
                    quantity = 3,
                    unitPrice = BigDecimal("2400"),
                    totalPrice = BigDecimal("7200")
                )
            )
            3L -> listOf(
                OrderItem(
                    id = 3,
                    orderId = orderId,
                    productId = 3,
                    productName = "Fresh Bread",
                    quantity = 4,
                    unitPrice = BigDecimal("800"),
                    totalPrice = BigDecimal("3200")
                )
            )
            4L -> listOf(
                OrderItem(
                    id = 4,
                    orderId = orderId,
                    productId = 4,
                    productName = "Mixed Groceries",
                    quantity = 1,
                    unitPrice = BigDecimal("5800"),
                    totalPrice = BigDecimal("5800")
                )
            )
            else -> listOf(
                OrderItem(
                    id = 5,
                    orderId = orderId,
                    productId = 5,
                    productName = "Sample Product",
                    quantity = 1,
                    unitPrice = BigDecimal("2800"),
                    totalPrice = BigDecimal("2800")
                )
            )
        }
    }

    private fun loadAlerts() {
        viewModelScope.launch {
            // Demo alerts with realistic data
            val demoAlerts = listOf(
                OrderAlert(
                    id = 1,
                    orderId = 1,
                    alertType = AlertType.ORDER_DELAYED,
                    title = "Order Delayed",
                    message = "Order #ORD-001 is running 30 minutes behind schedule",
                    priority = AlertPriority.HIGH,
                    createdAt = System.currentTimeMillis() - (15 * 60 * 1000) // 15 minutes ago
                ),
                OrderAlert(
                    id = 2,
                    orderId = 2,
                    alertType = AlertType.INVENTORY_LOW,
                    title = "Low Stock Alert",
                    message = "Premium Coffee is running low (5 units remaining)",
                    priority = AlertPriority.MEDIUM,
                    createdAt = System.currentTimeMillis() - (30 * 60 * 1000) // 30 minutes ago
                ),
                OrderAlert(
                    id = 3,
                    orderId = 3,
                    alertType = AlertType.CUSTOMER_COMPLAINT,
                    title = "Customer Feedback",
                    message = "Customer reported issue with Order #ORD-003",
                    priority = AlertPriority.LOW,
                    createdAt = System.currentTimeMillis() - (60 * 60 * 1000) // 1 hour ago
                )
            )
            _alerts.value = demoAlerts
        }
    }

    private fun updatePerformanceMetricsFromOrders(orders: List<Order>) {
        val metrics = PerformanceMetrics(
            totalOrders = orders.size,
            successOrders = orders.count { it.status == OrderStatus.SUCCESS },
            waitingOrders = orders.count { it.status == OrderStatus.WAITING },
            rejectedOrders = orders.count { it.status == OrderStatus.REJECT },
            successRate = if (orders.isNotEmpty()) {
                (orders.count { it.status == OrderStatus.SUCCESS }.toFloat() / orders.size) * 100
            } else 85f, // Default demo value
            totalRevenue = orders.filter { it.status == OrderStatus.SUCCESS }
                .sumOf { it.totalAmount }.takeIf { it > BigDecimal.ZERO }
                ?: BigDecimal("125000"), // Demo value in Kyats
            averageOrderValue = if (orders.isNotEmpty()) {
                orders.sumOf { it.totalAmount } / BigDecimal(orders.size)
            } else BigDecimal("4500"), // Demo value
            averageCollectionTime = 25, // Demo: 25 minutes
            topPerformingAgent = "John Doe",
            topPerformingProduct = "Premium Coffee",
            customerSatisfactionRate = 4.8f
        )

        _performanceMetrics.value = metrics
    }

    private fun loadPerformanceMetrics() {
        viewModelScope.launch {
            try {
                // Calculate performance metrics from orders
                val allOrders = orderRepository.getAllOrders().first()
                updatePerformanceMetricsFromOrders(allOrders)

            } catch (e: Exception) {
                // Provide fallback demo data if there's an error
                _performanceMetrics.value = PerformanceMetrics(
                    totalOrders = 45,
                    successOrders = 38,
                    waitingOrders = 7,
                    rejectedOrders = 2,
                    successRate = 85f,
                    totalRevenue = BigDecimal("125000"),
                    averageOrderValue = BigDecimal("4500"),
                    averageCollectionTime = 25,
                    topPerformingAgent = "John Doe",
                    topPerformingProduct = "Premium Coffee",
                    customerSatisfactionRate = 4.8f
                )
                _uiState.value = _uiState.value.copy(error = e.message)
            }
        }
    }

    private fun createTrackingEntry(orderId: Long, status: OrderStatus) {
        // Implementation for creating tracking entries
    }

    private fun generateStatusChangeAlert(orderId: Long, status: OrderStatus) {
        // Implementation for generating status change alerts
    }

    private fun generateAssignmentAlert(orderId: Long, agentId: Long) {
        // Implementation for generating assignment alerts
    }

    private fun getExportData(config: ExportConfig): List<Order> {
        // Implementation for getting export data
        return _uiState.value.orders
    }

    private fun generateCSVExport(data: List<Order>, config: ExportConfig) {
        // Implementation for CSV export
    }

    private fun generateExcelExport(data: List<Order>, config: ExportConfig) {
        // Implementation for Excel export
    }

    private fun generatePDFExport(data: List<Order>, config: ExportConfig) {
        // Implementation for PDF export
    }

    private fun generateJSONExport(data: List<Order>, config: ExportConfig) {
        // Implementation for JSON export
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun clearExportCompleted() {
        _uiState.value = _uiState.value.copy(exportCompleted = false)
    }

    private fun getDemoOrders(): List<Order> {
        val currentTime = System.currentTimeMillis()
        return listOf(
            Order(
                id = 1,
                orderNumber = "ORD-001",
                customerId = 1,
                customerName = "Walk In Customer",
                status = OrderStatus.WAITING,
                subtotal = BigDecimal("4500"),
                taxAmount = BigDecimal("0"),
                discountAmount = BigDecimal("0"),
                totalAmount = BigDecimal("4500"),
                notes = "Premium coffee order",
                createdAt = currentTime - (30 * 60 * 1000), // 30 minutes ago
                updatedAt = currentTime - (15 * 60 * 1000)
            ),
            Order(
                id = 2,
                orderNumber = "ORD-002",
                customerId = 2,
                customerName = "John Smith",
                status = OrderStatus.SUCCESS,
                subtotal = BigDecimal("7200"),
                taxAmount = BigDecimal("0"),
                discountAmount = BigDecimal("200"),
                totalAmount = BigDecimal("7000"),
                notes = "Bulk tea order",
                createdAt = currentTime - (2 * 60 * 60 * 1000), // 2 hours ago
                updatedAt = currentTime - (60 * 60 * 1000)
            ),
            Order(
                id = 3,
                orderNumber = "ORD-003",
                customerId = 3,
                customerName = "Mary Johnson",
                status = OrderStatus.WAITING,
                subtotal = BigDecimal("3200"),
                taxAmount = BigDecimal("0"),
                discountAmount = BigDecimal("0"),
                totalAmount = BigDecimal("3200"),
                notes = "Fresh bread order",
                createdAt = currentTime - (45 * 60 * 1000), // 45 minutes ago
                updatedAt = currentTime - (10 * 60 * 1000)
            ),
            Order(
                id = 4,
                orderNumber = "ORD-004",
                customerId = 1,
                customerName = "Walk In Customer",
                status = OrderStatus.SUCCESS,
                subtotal = BigDecimal("5800"),
                taxAmount = BigDecimal("0"),
                discountAmount = BigDecimal("300"),
                totalAmount = BigDecimal("5500"),
                notes = "Mixed grocery order",
                createdAt = currentTime - (90 * 60 * 1000), // 1.5 hours ago
                updatedAt = currentTime - (20 * 60 * 1000)
            ),
            Order(
                id = 5,
                orderNumber = "ORD-005",
                customerId = 4,
                customerName = "David Wilson",
                status = OrderStatus.REJECT,
                subtotal = BigDecimal("2800"),
                taxAmount = BigDecimal("0"),
                discountAmount = BigDecimal("0"),
                totalAmount = BigDecimal("2800"),
                notes = "Customer cancelled",
                createdAt = currentTime - (3 * 60 * 60 * 1000), // 3 hours ago
                updatedAt = currentTime - (2 * 60 * 60 * 1000)
            )
        )
    }
}

data class OrderManagementUiState(
    val isLoading: Boolean = false,
    val orders: List<Order> = emptyList(),
    val totalOrders: Int = 0,
    val currentFilter: OrderFilter = OrderFilter(),
    val sortBy: OrderSortBy = OrderSortBy.CREATED_DATE,
    val sortDirection: SortDirection = SortDirection.DESCENDING,
    val selectedOrderDetails: Order? = null,
    val selectedOrderItems: List<OrderItem> = emptyList(),
    val showOrderDetailsDialog: Boolean = false,
    val dashboardWidgets: List<DashboardWidget> = getDefaultWidgets(),
    val isExporting: Boolean = false,
    val exportCompleted: Boolean = false,
    val error: String? = null
)

enum class OrderSortBy {
    ORDER_NUMBER,
    CUSTOMER_NAME,
    STATUS,
    TOTAL_AMOUNT,
    CREATED_DATE,
    UPDATED_DATE
}

enum class SortDirection {
    ASCENDING,
    DESCENDING
}

fun getDefaultWidgets(): List<DashboardWidget> {
    return listOf(
        DashboardWidget(
            id = "recent_orders",
            title = "Recent Orders",
            type = WidgetType.RECENT_ORDERS,
            position = 0,
            size = WidgetSize.MEDIUM
        ),
        DashboardWidget(
            id = "agent_tracking",
            title = "Agent Tracking",
            type = WidgetType.AGENT_TRACKING,
            position = 1,
            size = WidgetSize.LARGE
        ),
        DashboardWidget(
            id = "order_status_overview",
            title = "Order Status Overview",
            type = WidgetType.ORDER_STATUS_OVERVIEW,
            position = 2,
            size = WidgetSize.LARGE
        ),
        DashboardWidget(
            id = "performance_metrics",
            title = "Performance Metrics",
            type = WidgetType.PERFORMANCE_METRICS,
            position = 3,
            size = WidgetSize.MEDIUM
        ),
        DashboardWidget(
            id = "alerts_notifications",
            title = "Alerts & Notifications",
            type = WidgetType.ALERTS_NOTIFICATIONS,
            position = 4,
            size = WidgetSize.MEDIUM
        ),
        DashboardWidget(
            id = "revenue_chart",
            title = "Revenue Chart",
            type = WidgetType.REVENUE_CHART,
            position = 5,
            size = WidgetSize.LARGE
        )
    )
}
