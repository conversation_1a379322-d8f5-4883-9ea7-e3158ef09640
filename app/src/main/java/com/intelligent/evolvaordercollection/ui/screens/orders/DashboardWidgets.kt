package com.intelligent.evolvaordercollection.ui.screens.orders

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.intelligent.evolvaordercollection.data.model.*
import com.intelligent.evolvaordercollection.ui.viewmodel.OrderManagementUiState
import com.intelligent.evolvaordercollection.utils.CurrencyUtils
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun DashboardWidgetCard(
    widget: DashboardWidget,
    uiState: OrderManagementUiState,
    alerts: List<OrderAlert>,
    performanceMetrics: PerformanceMetrics,
    onWidgetClick: () -> Unit,
    modifier: Modifier = Modifier,
    onNavigateToOrderEntry: () -> Unit = {},
    onNavigateToReports: () -> Unit = {},
    onOrderClick: (Order) -> Unit = {}
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(getWidgetHeight(widget.size))
            .clickable { onWidgetClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Widget Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = widget.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                
                Icon(
                    imageVector = getWidgetIcon(widget.type),
                    contentDescription = widget.title,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Widget Content
            when (widget.type) {
                WidgetType.ORDER_STATUS_OVERVIEW -> {
                    OrderStatusOverviewWidget(uiState.orders)
                }
                WidgetType.RECENT_ORDERS -> {
                    RecentOrdersWidget(
                        recentOrders = uiState.orders.take(5),
                        onOrderClick = onOrderClick
                    )
                }
                WidgetType.PERFORMANCE_METRICS -> {
                    PerformanceMetricsWidget(performanceMetrics)
                }
                WidgetType.ALERTS_NOTIFICATIONS -> {
                    AlertsNotificationsWidget(alerts.take(3))
                }
                WidgetType.AGENT_TRACKING -> {
                    AgentTrackingWidget()
                }
                WidgetType.REVENUE_CHART -> {
                    RevenueChartWidget(performanceMetrics)
                }
                WidgetType.ORDER_TRENDS -> {
                    OrderTrendsWidget(uiState.orders)
                }
                WidgetType.TOP_PRODUCTS -> {
                    TopProductsWidget()
                }
                WidgetType.CUSTOMER_INSIGHTS -> {
                    CustomerInsightsWidget()
                }
                WidgetType.MAP_VIEW -> {
                    MapViewWidget()
                }
                WidgetType.QUICK_ACTIONS -> {
                    QuickActionsWidget(
                        onNavigateToOrderEntry = onNavigateToOrderEntry,
                        onNavigateToReports = onNavigateToReports
                    )
                }
                WidgetType.COLLECTION_POINTS -> {
                    CollectionPointsWidget()
                }
            }
        }
    }
}

@Composable
fun OrderStatusOverviewWidget(orders: List<Order>) {
    val statusCounts = orders.groupBy { it.status }.mapValues { it.value.size }
    val relevantStatuses = OrderStatus.values().filter { statusCounts[it] ?: 0 > 0 }

    if (orders.isEmpty()) {
        // Show demo data if no orders
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            StatusRow("WAITING", 7, 35, MaterialTheme.colorScheme.primary)
            StatusRow("SUCCESS", 38, 76, MaterialTheme.colorScheme.tertiary)
            StatusRow("REJECT", 2, 10, MaterialTheme.colorScheme.secondary)
        }
    } else {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            relevantStatuses.forEach { status ->
                val count = statusCounts[status] ?: 0
                val percentage = if (orders.isNotEmpty()) (count.toFloat() / orders.size) * 100 else 0f

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp)
                                .clip(CircleShape)
                                .background(getStatusColor(status))
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = status.name.replace("_", " "),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    Text(
                        text = "$count (${percentage.toInt()}%)",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusRow(status: String, count: Int, percentage: Int, color: Color) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .clip(CircleShape)
                    .background(color)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = status,
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Text(
            text = "$count ($percentage%)",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun RecentOrdersWidget(
    recentOrders: List<Order>,
    onOrderClick: (Order) -> Unit = {}
) {
    if (recentOrders.isEmpty()) {
        // Show demo data if no orders
        val demoOrders = getDemoOrders()
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            demoOrders.forEach { order ->
                ClickableOrderRow(
                    order = order,
                    onClick = { onOrderClick(order) }
                )
            }
        }
    } else {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            recentOrders.forEach { order ->
                ClickableOrderRow(
                    order = order,
                    onClick = { onOrderClick(order) }
                )
            }
        }
    }
}

@Composable
private fun ClickableOrderRow(
    order: Order,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = order.orderNumber,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = order.customerName,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Column(horizontalAlignment = Alignment.End) {
            StatusChip(order.status)
            Text(
                text = CurrencyUtils.formatPrice(order.totalAmount),
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Composable
private fun DemoOrderRow(orderNumber: String, customer: String, status: String, amount: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = orderNumber,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = customer,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Column(horizontalAlignment = Alignment.End) {
            StatusChip(
                text = status,
                color = when (status) {
                    "WAITING" -> MaterialTheme.colorScheme.secondary
                    "SUCCESS" -> MaterialTheme.colorScheme.primary
                    "REJECT" -> MaterialTheme.colorScheme.error
                    else -> MaterialTheme.colorScheme.primary
                }
            )
            Text(
                text = amount,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Composable
fun PerformanceMetricsWidget(metrics: PerformanceMetrics) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        MetricRow(
            label = "Success Rate",
            value = "${metrics.successRate.toInt()}%",
            icon = Icons.Default.TrendingUp,
            color = MaterialTheme.colorScheme.primary
        )
        
        MetricRow(
            label = "Avg. Order Value",
            value = CurrencyUtils.formatPrice(metrics.averageOrderValue),
            icon = Icons.Default.AttachMoney,
            color = MaterialTheme.colorScheme.secondary
        )
        
        MetricRow(
            label = "Avg. Collection Time",
            value = "${metrics.averageCollectionTime} min",
            icon = Icons.Default.Schedule,
            color = MaterialTheme.colorScheme.tertiary
        )
    }
}

@Composable
fun MetricRow(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = color,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium
            )
        }
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@Composable
fun AlertsNotificationsWidget(alerts: List<OrderAlert>) {
    if (alerts.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "No alerts",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    } else {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(alerts) { alert ->
                AlertItem(alert)
            }
        }
    }
}

@Composable
fun AlertItem(alert: OrderAlert) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        Icon(
            imageVector = getAlertIcon(alert.alertType),
            contentDescription = alert.alertType.name,
            tint = getAlertColor(alert.priority),
            modifier = Modifier.size(16.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = alert.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = alert.message,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Composable
fun AgentTrackingWidget() {
    // Demo agent data
    val agents = listOf(
        "John Doe" to "Available",
        "Jane Smith" to "On Delivery",
        "Mike Johnson" to "Offline",
        "Sarah Wilson" to "Available"
    )

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        agents.forEach { (name, status) ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "Agent",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = name,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                StatusChip(
                    text = status,
                    color = when (status) {
                        "Available" -> MaterialTheme.colorScheme.primary
                        "On Delivery" -> MaterialTheme.colorScheme.secondary
                        else -> MaterialTheme.colorScheme.outline
                    }
                )
            }
        }
    }
}

@Composable
fun RevenueChartWidget(metrics: PerformanceMetrics) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = CurrencyUtils.formatPrice(metrics.totalRevenue),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Text(
            text = "Total Revenue",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Simple progress indicator as chart placeholder
        LinearProgressIndicator(
            progress = 0.75f,
            modifier = Modifier.fillMaxWidth(),
            color = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "75% of monthly target",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun OrderTrendsWidget(orders: List<Order>) {
    // Simple trend analysis
    val todayOrders = orders.filter { 
        val today = Calendar.getInstance()
        val orderDate = Calendar.getInstance().apply { timeInMillis = it.createdAt }
        today.get(Calendar.DAY_OF_YEAR) == orderDate.get(Calendar.DAY_OF_YEAR)
    }.size
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = todayOrders.toString(),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Text(
            text = "Orders Today",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.TrendingUp,
                contentDescription = "Trending up",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "+12% vs yesterday",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Composable
fun TopProductsWidget() {
    // Mock top products data
    val topProducts = listOf(
        "Premium Coffee" to 45,
        "Organic Tea" to 32,
        "Fresh Bread" to 28
    )
    
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(topProducts) { (product, sales) ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = product,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.weight(1f)
                )
                
                Text(
                    text = "$sales sold",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

@Composable
fun CustomerInsightsWidget() {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        MetricRow(
            label = "New Customers",
            value = "23",
            icon = Icons.Default.PersonAdd,
            color = MaterialTheme.colorScheme.primary
        )
        
        MetricRow(
            label = "Returning Customers",
            value = "67%",
            icon = Icons.Default.Repeat,
            color = MaterialTheme.colorScheme.secondary
        )
        
        MetricRow(
            label = "Customer Satisfaction",
            value = "4.8/5",
            icon = Icons.Default.Star,
            color = MaterialTheme.colorScheme.tertiary
        )
    }
}

@Composable
fun MapViewWidget() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Map,
                contentDescription = "Map",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Interactive Map",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
            Text(
                text = "Tap to view full map",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun QuickActionsWidget(
    onNavigateToOrderEntry: () -> Unit = {},
    onNavigateToReports: () -> Unit = {}
) {
    val actions = listOf(
        Triple("Create Order", Icons.Default.Add, onNavigateToOrderEntry),
        Triple("Assign Agent", Icons.Default.Person) { /* Handle assign agent */ },
        Triple("View Reports", Icons.Default.Analytics, onNavigateToReports),
        Triple("Settings", Icons.Default.Settings) { /* Handle settings */ }
    )

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(actions) { (action, icon, onClick) ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onClick() }
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = action,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = action,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
fun CollectionPointsWidget() {
    // Mock collection points data
    val collectionPoints = listOf(
        "Downtown Hub" to "85% capacity",
        "North Station" to "45% capacity",
        "South Center" to "92% capacity"
    )
    
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(collectionPoints) { (point, capacity) ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = point,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.weight(1f)
                )
                
                Text(
                    text = capacity,
                    style = MaterialTheme.typography.bodySmall,
                    color = when {
                        capacity.contains("9") -> MaterialTheme.colorScheme.error
                        capacity.contains("8") -> MaterialTheme.colorScheme.tertiary
                        else -> MaterialTheme.colorScheme.primary
                    }
                )
            }
        }
    }
}

// Helper functions
fun getWidgetHeight(size: WidgetSize): androidx.compose.ui.unit.Dp {
    return when (size) {
        WidgetSize.SMALL -> 120.dp      // Reduced from 150.dp
        WidgetSize.MEDIUM -> 180.dp     // Reduced from 220.dp
        WidgetSize.LARGE -> 240.dp      // Reduced from 300.dp
        WidgetSize.EXTRA_LARGE -> 320.dp // Reduced from 400.dp
    }
}

fun getWidgetIcon(type: WidgetType): androidx.compose.ui.graphics.vector.ImageVector {
    return when (type) {
        WidgetType.ORDER_STATUS_OVERVIEW -> Icons.Default.Dashboard
        WidgetType.RECENT_ORDERS -> Icons.Default.History
        WidgetType.PERFORMANCE_METRICS -> Icons.Default.Analytics
        WidgetType.ALERTS_NOTIFICATIONS -> Icons.Default.Notifications
        WidgetType.AGENT_TRACKING -> Icons.Default.LocationOn
        WidgetType.REVENUE_CHART -> Icons.Default.TrendingUp
        WidgetType.ORDER_TRENDS -> Icons.Default.ShowChart
        WidgetType.TOP_PRODUCTS -> Icons.Default.Star
        WidgetType.CUSTOMER_INSIGHTS -> Icons.Default.People
        WidgetType.MAP_VIEW -> Icons.Default.Map
        WidgetType.QUICK_ACTIONS -> Icons.Default.Speed
        WidgetType.COLLECTION_POINTS -> Icons.Default.LocationCity
    }
}

private fun getDemoOrders(): List<Order> {
    return listOf(
        Order(
            id = 1L,
            orderNumber = "ORD-001",
            customerName = "Walk In Customer",
            status = OrderStatus.WAITING,
            subtotal = java.math.BigDecimal("4500"),
            taxAmount = java.math.BigDecimal("0"),
            discountAmount = java.math.BigDecimal("0"),
            totalAmount = java.math.BigDecimal("4500"),
            notes = "Demo order for testing purposes",
            createdAt = System.currentTimeMillis() - (2 * 60 * 60 * 1000), // 2 hours ago
            updatedAt = System.currentTimeMillis() - (1 * 60 * 60 * 1000)  // 1 hour ago
        ),
        Order(
            id = 2L,
            orderNumber = "ORD-002",
            customerName = "John Smith",
            status = OrderStatus.SUCCESS,
            subtotal = java.math.BigDecimal("7000"),
            taxAmount = java.math.BigDecimal("0"),
            discountAmount = java.math.BigDecimal("0"),
            totalAmount = java.math.BigDecimal("7000"),
            notes = "Completed demo order",
            createdAt = System.currentTimeMillis() - (4 * 60 * 60 * 1000), // 4 hours ago
            updatedAt = System.currentTimeMillis() - (30 * 60 * 1000)      // 30 minutes ago
        ),
        Order(
            id = 3L,
            orderNumber = "ORD-003",
            customerName = "Mary Johnson",
            status = OrderStatus.WAITING,
            subtotal = java.math.BigDecimal("3200"),
            taxAmount = java.math.BigDecimal("0"),
            discountAmount = java.math.BigDecimal("0"),
            totalAmount = java.math.BigDecimal("3200"),
            notes = "Pending demo order",
            createdAt = System.currentTimeMillis() - (1 * 60 * 60 * 1000), // 1 hour ago
            updatedAt = System.currentTimeMillis() - (15 * 60 * 1000)      // 15 minutes ago
        )
    )
}

fun getStatusColor(status: OrderStatus): Color {
    return when (status) {
        OrderStatus.WAITING -> Color(0xFFFF9800) // Orange for waiting
        OrderStatus.SUCCESS -> Color(0xFF4CAF50) // Green for success
        OrderStatus.REJECT -> Color(0xFFF44336)  // Red for reject
    }
}

fun getAlertIcon(type: AlertType): androidx.compose.ui.graphics.vector.ImageVector {
    return when (type) {
        AlertType.ORDER_DELAYED -> Icons.Default.Schedule
        AlertType.AGENT_UNAVAILABLE -> Icons.Default.PersonOff
        AlertType.COLLECTION_POINT_FULL -> Icons.Default.Warning
        AlertType.CUSTOMER_COMPLAINT -> Icons.Default.Feedback
        AlertType.SYSTEM_ERROR -> Icons.Default.Error
        AlertType.PAYMENT_ISSUE -> Icons.Default.Payment
        AlertType.INVENTORY_LOW -> Icons.Default.Inventory
        AlertType.DELIVERY_FAILED -> Icons.Default.LocalShipping
    }
}

fun getAlertColor(priority: AlertPriority): Color {
    return when (priority) {
        AlertPriority.LOW -> Color(0xFF4CAF50)
        AlertPriority.MEDIUM -> Color(0xFFFF9800)
        AlertPriority.HIGH -> Color(0xFFF44336)
        AlertPriority.CRITICAL -> Color(0xFF9C27B0)
    }
}

@Composable
fun StatusChip(
    status: OrderStatus,
    modifier: Modifier = Modifier
) {
    StatusChip(
        text = status.name.replace("_", " "),
        color = getStatusColor(status),
        modifier = modifier
    )
}

@Composable
fun StatusChip(
    text: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}
