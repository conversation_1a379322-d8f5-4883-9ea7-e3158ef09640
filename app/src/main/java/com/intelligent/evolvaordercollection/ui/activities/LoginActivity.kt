package com.intelligent.evolvaordercollection.ui.activities

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.intelligent.evolvaordercollection.MainActivity
import com.intelligent.evolvaordercollection.ui.screens.auth.LoginScreen
import com.intelligent.evolvaordercollection.ui.theme.EvolvaOrderCollectionTheme
import com.intelligent.evolvaordercollection.data.repository.UserSessionManager

class LoginActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Initialize UserSessionManager
        UserSessionManager.initialize(this)
        
        // Check if user is already logged in
        val sessionManager = UserSessionManager.getInstance()
        if (sessionManager.isLoggedIn()) {
            // User is already logged in, go to main activity
            navigateToMainActivity()
            return
        }
        
        setContent {
            EvolvaOrderCollectionTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    LoginScreen(
                        onLoginSuccess = {
                            navigateToMainActivity()
                        }
                    )
                }
            }
        }
    }
    
    private fun navigateToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish() // Close login activity
    }
    
    override fun onBackPressed() {
        // Prevent going back from login screen
        // User must login to proceed
        super.onBackPressed()
        finishAffinity() // Close the entire app
    }
}
