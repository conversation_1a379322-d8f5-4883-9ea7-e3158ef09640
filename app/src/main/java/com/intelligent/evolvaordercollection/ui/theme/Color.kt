package com.intelligent.evolvaordercollection.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// POS Theme Colors
val POSPrimary = Color(0xFF1976D2)
val POSPrimaryVariant = Color(0xFF1565C0)
val POSSecondary = Color(0xFF03DAC6)
val POSSecondaryVariant = Color(0xFF018786)
val POSBackground = Color(0xFFF5F5F5)
val POSSurface = Color(0xFFFFFFFF)
val POSError = Color(0xFFB00020)
val POSOnPrimary = Color(0xFFFFFFFF)
val POSOnSecondary = Color(0xFF000000)
val POSOnBackground = Color(0xFF000000)
val POSOnSurface = Color(0xFF000000)
val POSOnError = Color(0xFFFFFFFF)

// Status Colors
val SuccessColor = Color(0xFF4CAF50)
val WarningColor = Color(0xFFFF9800)
val InfoColor = Color(0xFF2196F3)

// Card Colors
val CardBackground = Color(0xFFFFFFFF)
val CardElevation = Color(0x1F000000)

// Text Colors
val TextPrimary = Color(0xFF212121)
val TextSecondary = Color(0xFF757575)
val TextHint = Color(0xFFBDBDBD)
