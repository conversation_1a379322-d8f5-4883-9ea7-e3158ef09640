package com.intelligent.evolvaordercollection

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController

import com.intelligent.evolvaordercollection.ui.components.CollapsibleSidebarNavigation
import com.intelligent.evolvaordercollection.ui.navigation.POSNavigation
import com.intelligent.evolvaordercollection.ui.navigation.Screen
import com.intelligent.evolvaordercollection.ui.theme.CompactTypography
import com.intelligent.evolvaordercollection.ui.theme.EvolvaOrderCollectionTheme
import com.intelligent.evolvaordercollection.ui.theme.Typography
import com.intelligent.evolvaordercollection.utils.ResponsiveUtils
import com.intelligent.evolvaordercollection.ui.activities.LoginActivity
import com.intelligent.evolvaordercollection.data.repository.UserSessionManager
import android.content.Intent
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize UserSessionManager
        UserSessionManager.initialize(this)

        // Check if user is logged in
        val sessionManager = UserSessionManager.getInstance()
        if (!sessionManager.isLoggedIn()) {
            // User is not logged in, redirect to login
            val intent = Intent(this, LoginActivity::class.java)
            startActivity(intent)
            finish() // Close main activity
            return
        }

        enableEdgeToEdge()
        setContent {
            MainContent()
        }
    }
}

@Composable
private fun MainContent() {
    val isCompact = ResponsiveUtils.isCompactScreen()
    val shouldUseSinglePane = ResponsiveUtils.shouldUseSinglePane()

    // Use responsive typography
    val typography = if (isCompact) CompactTypography else Typography

    EvolvaOrderCollectionTheme {
        CompositionLocalProvider(
            LocalTextStyle provides typography.bodyMedium
        ) {
            MaterialTheme(
                typography = typography
            ) {
                if (shouldUseSinglePane) {
                    // Mobile layout with overlay sidebar
                    MobileLayoutWithSidebar()
                } else {
                    // Tablet layout with persistent sidebar
                    TabletLayoutWithSidebar()
                }
            }
        }
    }
}

@Composable
private fun MobileLayoutWithSidebar() {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    var isSidebarExpanded by remember { mutableStateOf(false) }
    var showSidebarOverlay by remember { mutableStateOf(false) }

    // Auto-collapse sidebar when navigating to Order Entry
    LaunchedEffect(currentRoute) {
        if (currentRoute == Screen.OrderEntry.route) {
            isSidebarExpanded = false
            showSidebarOverlay = false
        } else {
            isSidebarExpanded = true
        }
    }

    Box(modifier = Modifier.fillMaxSize().systemBarsPadding()) {
        // Main content
        Row(modifier = Modifier.fillMaxSize()) {
            // Collapsible sidebar
            CollapsibleSidebarNavigation(
                navController = navController,
                currentRoute = currentRoute,
                isExpanded = isSidebarExpanded,
                onToggleExpanded = {
                    isSidebarExpanded = !isSidebarExpanded
                    showSidebarOverlay = isSidebarExpanded
                },
                modifier = Modifier.statusBarsPadding()
            )

            // Content area
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .weight(1f)
            ) {
                POSNavigation(navController = navController)
            }
        }

        // Overlay for mobile when sidebar is expanded
        if (showSidebarOverlay && isSidebarExpanded) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .offset(x = if (isSidebarExpanded) 200.dp else 0.dp)
            ) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
                    onClick = {
                        isSidebarExpanded = false
                        showSidebarOverlay = false
                    }
                ) {}
            }
        }
    }
}

@Composable
private fun TabletLayoutWithSidebar() {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    var isSidebarExpanded by remember { mutableStateOf(true) }

    // Auto-collapse sidebar when navigating to Order Entry
    LaunchedEffect(currentRoute) {
        if (currentRoute == Screen.OrderEntry.route) {
            isSidebarExpanded = false
        } else {
            isSidebarExpanded = true
        }
    }

    Row(modifier = Modifier.fillMaxSize().systemBarsPadding()) {
        // Persistent sidebar for tablets
        CollapsibleSidebarNavigation(
            navController = navController,
            currentRoute = currentRoute,
            isExpanded = isSidebarExpanded,
            onToggleExpanded = { isSidebarExpanded = !isSidebarExpanded }
        )

        // Content area
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            POSNavigation(navController = navController)
        }
    }
}
