package com.intelligent.evolvaordercollection.di

import android.content.Context
import androidx.room.Room
import com.google.firebase.firestore.FirebaseFirestore
import com.intelligent.evolvaordercollection.data.database.POSDatabase
import com.intelligent.evolvaordercollection.data.repository.*

/**
 * Simple dependency injection container for the POS application.
 * This replaces Hilt with a manual DI approach that's fully compatible with Kotlin 2.0+
 */
class AppContainer(private val context: Context) {
    
    // Database
    val database: POSDatabase by lazy {
        POSDatabase.getDatabase(context)
    }
    
    // Repositories
    val productRepository: ProductRepository by lazy {
        ProductRepository(database.productDao())
    }
    
    val customerRepository: CustomerRepository by lazy {
        CustomerRepository(database.customerDao())
    }
    
    val orderRepository: OrderRepository by lazy {
        OrderRepository(database.orderDao(), database.orderItemDao())
    }
    
    val orderItemRepository: OrderItemRepository by lazy {
        OrderItemRepository(database.orderItemDao())
    }

    // Export repository
    val exportRepository: ExportRepository by lazy {
        ExportRepository(database, context)
    }

    // Firebase Firestore instance
    val firestore: FirebaseFirestore by lazy {
        FirebaseFirestore.getInstance()
    }

    // Sync repository
    val syncRepository: SyncRepository by lazy {
        SyncRepository(database, firestore, context)
    }
}
