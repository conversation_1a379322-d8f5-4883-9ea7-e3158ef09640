package com.intelligent.evolvaordercollection.data.repository

import com.intelligent.evolvaordercollection.data.database.dao.OrderItemDao
import com.intelligent.evolvaordercollection.data.model.OrderItem
import kotlinx.coroutines.flow.Flow
import java.math.BigDecimal

class OrderItemRepository(
    private val orderItemDao: OrderItemDao
) {
    
    fun getOrderItems(orderId: Long): Flow<List<OrderItem>> = orderItemDao.getOrderItems(orderId)
    
    suspend fun getOrderItemById(id: Long): OrderItem? = orderItemDao.getOrderItemById(id)
    
    suspend fun insertOrderItem(orderItem: OrderItem): Long = orderItemDao.insertOrderItem(orderItem)
    
    suspend fun insertOrderItems(orderItems: List<OrderItem>) = orderItemDao.insertOrderItems(orderItems)
    
    suspend fun updateOrderItem(orderItem: OrderItem) = orderItemDao.updateOrderItem(orderItem)
    
    suspend fun deleteOrderItem(orderItem: OrderItem) = orderItemDao.deleteOrderItem(orderItem)
    
    suspend fun deleteOrderItems(orderId: Long) = orderItemDao.deleteOrderItems(orderId)
    
    suspend fun updateOrderItemQuantity(id: Long, quantity: Int, totalPrice: BigDecimal) = 
        orderItemDao.updateOrderItemQuantity(id, quantity, totalPrice.toString())
}
