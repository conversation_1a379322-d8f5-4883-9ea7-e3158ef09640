package com.intelligent.evolvaordercollection.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.math.BigDecimal

// Sync status for tracking data synchronization
@Entity(tableName = "sync_status")
data class SyncStatus(
    @PrimaryKey
    val id: String,
    val tableName: String,
    val recordId: Long,
    val action: SyncAction,
    val timestamp: Long = System.currentTimeMillis(),
    val synced: Boolean = false,
    val retryCount: Int = 0,
    val errorMessage: String? = null
)

enum class SyncAction {
    CREATE, UPDATE, DELETE
}

// Cloud versions of our entities for Firebase
data class CloudProduct(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val price: Double = 0.0, // Firebase doesn't support BigDecimal directly
    val category: String = "",
    val productCode: String = "",
    val barcode: String = "",
    val stockQuantity: Int = 0,
    val imageUrl: String = "",
    val isActive: Boolean = true,
    val createdAt: Long = 0L,
    val updatedAt: Long = 0L,
    val deviceId: String = "",
    val lastSyncedAt: Long = 0L
) {
    // Convert to local Product
    fun toProduct(): Product {
        return Product(
            id = id.toLongOrNull() ?: 0L,
            name = name,
            description = description,
            price = BigDecimal.valueOf(price),
            category = category,
            productCode = productCode,
            barcode = barcode,
            stockQuantity = stockQuantity,
            imageUrl = imageUrl,
            isActive = isActive,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
}

data class CloudCustomer(
    val id: String = "",
    val name: String = "",
    val email: String = "",
    val phone: String = "",
    val address: String = "",
    val customerCode: String = "",
    val contactPerson: String = "",
    val creditLimit: Double = 0.0,
    val townshipId: Int? = null,
    val townshipName: String = "",
    val saleTeamId: Int? = null,
    val saleTeamName: String = "",
    val latitude: Double? = null,
    val longitude: Double? = null,
    val isActive: Boolean = true,
    val createdAt: Long = 0L,
    val updatedAt: Long = 0L,
    val deviceId: String = "",
    val lastSyncedAt: Long = 0L
) {
    fun toCustomer(): Customer {
        return Customer(
            id = id.toLongOrNull() ?: 0L,
            name = name,
            email = email,
            phone = phone,
            address = address,
            customerCode = customerCode,
            contactPerson = contactPerson,
            creditLimit = java.math.BigDecimal.valueOf(creditLimit),
            townshipId = townshipId,
            townshipName = townshipName,
            saleTeamId = saleTeamId,
            saleTeamName = saleTeamName,
            latitude = latitude,
            longitude = longitude,
            isActive = isActive,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
}

data class CloudOrder(
    val id: String = "",
    val customerId: String = "",
    val customerName: String = "",
    val totalAmount: Double = 0.0,
    val status: String = "",
    val notes: String = "",
    val createdAt: Long = 0L,
    val updatedAt: Long = 0L,
    val deviceId: String = "",
    val lastSyncedAt: Long = 0L,
    val items: List<CloudOrderItem> = emptyList()
) {
    fun toOrder(): Order {
        return Order(
            id = id.toLongOrNull() ?: 0L,
            orderNumber = "ORD-${id}",
            customerId = customerId.toLongOrNull()?.takeIf { it > 0 },
            customerName = customerName,
            status = try { OrderStatus.valueOf(status) } catch (e: Exception) { OrderStatus.WAITING },
            totalAmount = BigDecimal.valueOf(totalAmount),
            notes = notes,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
}

data class CloudOrderItem(
    val id: String = "",
    val orderId: String = "",
    val productId: String = "",
    val productName: String = "",
    val quantity: Int = 0,
    val unitPrice: Double = 0.0,
    val totalPrice: Double = 0.0,
    val discount: Double = 0.0,
    val deviceId: String = "",
    val lastSyncedAt: Long = 0L
) {
    fun toOrderItem(): OrderItem {
        return OrderItem(
            id = id.toLongOrNull() ?: 0L,
            orderId = orderId.toLongOrNull() ?: 0L,
            productId = productId.toLongOrNull() ?: 0L,
            productName = productName,
            quantity = quantity,
            unitPrice = BigDecimal.valueOf(unitPrice),
            totalPrice = BigDecimal.valueOf(totalPrice)
            // Note: OrderItem doesn't have discount field, so we ignore it
        )
    }
}

// Device info for multi-device sync
data class DeviceInfo(
    val deviceId: String = "",
    val deviceName: String = "",
    val appVersion: String = "",
    val lastSyncAt: Long = 0L,
    val isActive: Boolean = true,
    val userId: String = ""
)

// Sync configuration
data class SyncConfig(
    val autoSyncEnabled: Boolean = true,
    val syncIntervalMinutes: Int = 15,
    val wifiOnlySync: Boolean = false,
    val lastFullSyncAt: Long = 0L,
    val conflictResolution: ConflictResolution = ConflictResolution.LATEST_WINS
)

enum class ConflictResolution {
    LATEST_WINS, MANUAL_RESOLVE, DEVICE_PRIORITY
}

// Export data models
data class ExportData(
    val products: List<Product> = emptyList(),
    val customers: List<Customer> = emptyList(),
    val orders: List<Order> = emptyList(),
    val orderItems: List<OrderItem> = emptyList(),
    val exportedAt: Long = System.currentTimeMillis(),
    val deviceId: String = "",
    val appVersion: String = ""
)

// Backup metadata
data class BackupMetadata(
    val backupId: String = "",
    val deviceId: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val dataVersion: Long = 0L,
    val recordCounts: Map<String, Int> = emptyMap(),
    val fileSize: Long = 0L,
    val checksum: String = ""
)
