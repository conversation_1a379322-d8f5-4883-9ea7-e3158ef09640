package com.intelligent.evolvaordercollection.data.model

import com.google.gson.annotations.SerializedName

/**
 * Order request model for creating orders
 */
data class CreateOrderRequest(
    @SerializedName("customerCode")
    val customerCode: String,
    
    @SerializedName("stationId")
    val stationId: Int,
    
    @SerializedName("orderItems")
    val orderItems: List<OrderItemRequest>,
    
    @SerializedName("totalAmount")
    val totalAmount: Double,
    
    @SerializedName("remarks")
    val remarks: String = ""
)

/**
 * Order item request model
 */
data class OrderItemRequest(
    @SerializedName("productCode")
    val productCode: String,
    
    @SerializedName("productName")
    val productName: String,
    
    @SerializedName("quantity")
    val quantity: Double,
    
    @SerializedName("unitPrice")
    val unitPrice: Double
)

/**
 * Order creation response model
 */
data class CreateOrderResponse(
    @SerializedName("success")
    val success: <PERSON><PERSON><PERSON>,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: OrderResponseData?
)

/**
 * Order response data model
 */
data class OrderResponseData(
    @SerializedName("OrderRequestID")
    val orderRequestId: Int,
    
    @SerializedName("OrderCollectionNumber")
    val orderCollectionNumber: String,
    
    @SerializedName("CustomerCode")
    val customerCode: String,
    
    @SerializedName("StationID")
    val stationId: Int,
    
    @SerializedName("TotalAmount")
    val totalAmount: Double,
    
    @SerializedName("Status")
    val status: String,
    
    @SerializedName("OrderItems")
    val orderItems: List<OrderItemResponse>,
    
    @SerializedName("ItemCount")
    val itemCount: Int
)

/**
 * Order item response model
 */
data class OrderItemResponse(
    @SerializedName("OrderRequestDetailID")
    val orderRequestDetailId: Int,
    
    @SerializedName("ProductCode")
    val productCode: String,
    
    @SerializedName("ProductName")
    val productName: String,
    
    @SerializedName("Quantity")
    val quantity: Double,
    
    @SerializedName("UnitPrice")
    val unitPrice: Double,
    
    @SerializedName("TotalPrice")
    val totalPrice: Double
)
