package com.intelligent.evolvaordercollection.data.repository

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.intelligent.evolvaordercollection.data.model.User
import com.intelligent.evolvaordercollection.data.model.UserSession

/**
 * Manages user session data using SharedPreferences
 */
class UserSessionManager private constructor(context: Context) {
    
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(
        PREF_NAME, Context.MODE_PRIVATE
    )
    private val gson = Gson()
    
    companion object {
        private const val PREF_NAME = "user_session"
        private const val KEY_USER_SESSION = "current_user_session"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        
        @Volatile
        private var INSTANCE: UserSessionManager? = null
        
        fun getInstance(context: Context? = null): UserSessionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: if (context != null) {
                    UserSessionManager(context.applicationContext).also { INSTANCE = it }
                } else {
                    throw IllegalStateException("UserSessionManager not initialized. Call getInstance(context) first.")
                }
            }
        }

        fun initialize(context: Context) {
            if (INSTANCE == null) {
                synchronized(this) {
                    if (INSTANCE == null) {
                        INSTANCE = UserSessionManager(context.applicationContext)
                    }
                }
            }
        }
    }
    
    /**
     * Save user session
     */
    fun saveSession(userSession: UserSession) {
        val editor = sharedPreferences.edit()
        editor.putString(KEY_USER_SESSION, gson.toJson(userSession))
        editor.putBoolean(KEY_IS_LOGGED_IN, true)
        editor.apply()
    }
    
    /**
     * Get current user session
     */
    fun getCurrentSession(): UserSession? {
        val isLoggedIn = sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
        if (!isLoggedIn) return null
        
        val sessionJson = sharedPreferences.getString(KEY_USER_SESSION, null)
        return if (sessionJson != null) {
            try {
                gson.fromJson(sessionJson, UserSession::class.java)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }
    
    /**
     * Get current logged in user
     */
    fun getCurrentUser(): User? {
        return getCurrentSession()?.user
    }
    
    /**
     * Check if user is logged in
     */
    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false) && getCurrentSession() != null
    }
    
    /**
     * Get current user's sale team ID
     */
    fun getCurrentSaleTeamId(): Int {
        return getCurrentUser()?.saleTeamId ?: 0
    }
    
    /**
     * Get current user's employee ID
     */
    fun getCurrentEmployeeId(): Int {
        return getCurrentUser()?.employeeId ?: 0
    }
    
    /**
     * Get current user's station number
     */
    fun getCurrentStationNo(): String {
        return getCurrentSession()?.getStationNo() ?: "ST01"
    }
    
    /**
     * Get current user's name
     */
    fun getCurrentUserName(): String {
        return getCurrentUser()?.userName ?: ""
    }
    
    /**
     * Clear user session (logout)
     */
    fun clearSession() {
        val editor = sharedPreferences.edit()
        editor.remove(KEY_USER_SESSION)
        editor.putBoolean(KEY_IS_LOGGED_IN, false)
        editor.apply()
    }
    
    /**
     * Update session data
     */
    fun updateSession(userSession: UserSession) {
        saveSession(userSession)
    }
}
