package com.intelligent.evolvaordercollection.data.repository

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.intelligent.evolvaordercollection.data.database.POSDatabase
import com.intelligent.evolvaordercollection.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await

class SyncRepository(
    private val database: POSDatabase,
    private val firestore: FirebaseFirestore,
    private val context: Context
) {
    private val deviceId = generateDeviceId(context)
    
    // Collections in Firestore
    private val productsCollection = firestore.collection("products")
    private val customersCollection = firestore.collection("customers")
    private val ordersCollection = firestore.collection("orders")
    private val devicesCollection = firestore.collection("devices")
    private val syncStatusCollection = firestore.collection("sync_status")
    
    // Check network connectivity
    fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        
        return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
               capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
               capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
    }
    
    // Register device for multi-device sync
    suspend fun registerDevice(): Result<Unit> {
        return try {
            val deviceInfo = DeviceInfo(
                deviceId = deviceId,
                deviceName = getDeviceName(),
                appVersion = "1.0.0", // Get from BuildConfig
                lastSyncAt = System.currentTimeMillis(),
                isActive = true,
                userId = "default_user" // Implement proper user management
            )
            
            devicesCollection.document(deviceId).set(deviceInfo).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Sync products to cloud
    suspend fun syncProductsToCloud(): Result<Int> {
        return try {
            val localProducts = database.productDao().getAllProductsList()
            var syncedCount = 0

            localProducts.forEach { product ->
                if (product.isValidForSync()) {
                    val cloudProduct = product.toCloudProduct(deviceId)
                    productsCollection.document(product.id.toString()).set(cloudProduct).await()
                    syncedCount++
                }
            }

            Result.success(syncedCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Sync products from cloud
    suspend fun syncProductsFromCloud(): Result<Int> {
        return try {
            val cloudProducts = productsCollection
                .whereNotEqualTo("deviceId", deviceId)
                .get()
                .await()
                .toObjects(CloudProduct::class.java)
            
            var syncedCount = 0
            
            cloudProducts.forEach { cloudProduct ->
                val localProduct = cloudProduct.toProduct()
                val existingProduct = database.productDao().getProductById(localProduct.id)
                
                if (existingProduct == null) {
                    // New product from another device
                    database.productDao().insertProduct(localProduct)
                    syncedCount++
                } else if (cloudProduct.lastSyncedAt > existingProduct.updatedAt) {
                    // Update if cloud version is newer
                    database.productDao().updateProduct(localProduct)
                    syncedCount++
                }
            }
            
            Result.success(syncedCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Sync customers to cloud
    suspend fun syncCustomersToCloud(): Result<Int> {
        return try {
            val localCustomers = database.customerDao().getAllCustomersList()
            var syncedCount = 0

            localCustomers.forEach { customer ->
                if (customer.isValidForSync()) {
                    val cloudCustomer = customer.toCloudCustomer(deviceId)
                    customersCollection.document(customer.id.toString()).set(cloudCustomer).await()
                    syncedCount++
                }
            }

            Result.success(syncedCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Sync customers from cloud
    suspend fun syncCustomersFromCloud(): Result<Int> {
        return try {
            val cloudCustomers = customersCollection
                .whereNotEqualTo("deviceId", deviceId)
                .get()
                .await()
                .toObjects(CloudCustomer::class.java)
            
            var syncedCount = 0
            
            cloudCustomers.forEach { cloudCustomer ->
                val localCustomer = cloudCustomer.toCustomer()
                val existingCustomer = database.customerDao().getCustomerById(localCustomer.id)
                
                if (existingCustomer == null) {
                    database.customerDao().insertCustomer(localCustomer)
                    syncedCount++
                } else if (cloudCustomer.lastSyncedAt > existingCustomer.updatedAt) {
                    database.customerDao().updateCustomer(localCustomer)
                    syncedCount++
                }
            }
            
            Result.success(syncedCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Sync orders to cloud
    suspend fun syncOrdersToCloud(): Result<Int> {
        return try {
            val localOrders = database.orderDao().getAllOrdersList()
            var syncedCount = 0

            localOrders.forEach { order ->
                if (order.isValidForSync()) {
                    val orderItems = database.orderItemDao().getOrderItemsList(order.id)
                    val cloudOrder = order.toCloudOrder(deviceId, orderItems)
                    ordersCollection.document(order.id.toString()).set(cloudOrder).await()
                    syncedCount++
                }
            }

            Result.success(syncedCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Sync orders from cloud
    suspend fun syncOrdersFromCloud(): Result<Int> {
        return try {
            val cloudOrders = ordersCollection
                .whereNotEqualTo("deviceId", deviceId)
                .get()
                .await()
                .toObjects(CloudOrder::class.java)
            
            var syncedCount = 0
            
            cloudOrders.forEach { cloudOrder ->
                val localOrder = cloudOrder.toOrder()
                val existingOrder = database.orderDao().getOrderById(localOrder.id)
                
                if (existingOrder == null) {
                    // Insert new order and its items
                    database.orderDao().insertOrder(localOrder)
                    cloudOrder.items.forEach { cloudItem ->
                        database.orderItemDao().insertOrderItem(cloudItem.toOrderItem())
                    }
                    syncedCount++
                } else if (cloudOrder.lastSyncedAt > existingOrder.updatedAt) {
                    // Update existing order
                    database.orderDao().updateOrder(localOrder)
                    // Update order items (simplified - in production, handle item-level conflicts)
                    database.orderItemDao().deleteOrderItems(localOrder.id)
                    cloudOrder.items.forEach { cloudItem ->
                        database.orderItemDao().insertOrderItem(cloudItem.toOrderItem())
                    }
                    syncedCount++
                }
            }
            
            Result.success(syncedCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Full bidirectional sync
    suspend fun performFullSync(): Flow<SyncProgress> = flow {
        emit(SyncProgress("Starting sync...", 0))
        
        if (!isNetworkAvailable()) {
            emit(SyncProgress("No network connection", 0, isError = true))
            return@flow
        }
        
        try {
            // Register device
            emit(SyncProgress("Registering device...", 10))
            registerDevice()
            
            // Sync to cloud
            emit(SyncProgress("Uploading products...", 25))
            syncProductsToCloud()
            
            emit(SyncProgress("Uploading customers...", 40))
            syncCustomersToCloud()
            
            emit(SyncProgress("Uploading orders...", 55))
            syncOrdersToCloud()
            
            // Sync from cloud
            emit(SyncProgress("Downloading products...", 70))
            syncProductsFromCloud()
            
            emit(SyncProgress("Downloading customers...", 85))
            syncCustomersFromCloud()
            
            emit(SyncProgress("Downloading orders...", 95))
            syncOrdersFromCloud()
            
            emit(SyncProgress("Sync completed successfully!", 100, isComplete = true))
            
        } catch (e: Exception) {
            emit(SyncProgress("Sync failed: ${e.message}", 0, isError = true))
        }
    }
    
    // Get sync status
    suspend fun getSyncStatus(): SyncStatusInfo {
        val lastSyncTime = context.getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
            .getLong("last_sync_time", 0L)
        
        val pendingChanges = database.productDao().getAllProductsList().size +
                           database.customerDao().getAllCustomersList().size +
                           database.orderDao().getAllOrdersList().size
        
        return SyncStatusInfo(
            lastSyncTime = lastSyncTime,
            pendingChanges = pendingChanges,
            isOnline = isNetworkAvailable(),
            deviceId = deviceId
        )
    }
}

data class SyncProgress(
    val message: String,
    val progress: Int,
    val isError: Boolean = false,
    val isComplete: Boolean = false
)

data class SyncStatusInfo(
    val lastSyncTime: Long,
    val pendingChanges: Int,
    val isOnline: Boolean,
    val deviceId: String
)
