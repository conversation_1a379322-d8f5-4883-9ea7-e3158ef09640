package com.intelligent.evolvaordercollection.data.repository

import com.intelligent.evolvaordercollection.data.model.RemoteCustomer
import com.intelligent.evolvaordercollection.data.network.NetworkModule
import com.intelligent.evolvaordercollection.data.network.NetworkResult
import com.intelligent.evolvaordercollection.data.network.ProductApiService
import com.intelligent.evolvaordercollection.data.network.safeApiCall
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * Repository for handling remote customer data from MySQL database
 */
class RemoteCustomerRepository {
    
    private val apiService: ProductApiService = NetworkModule.provideProductApiService()
    
    /**
     * Get all customers with optional filters
     */
    fun getCustomers(
        search: String? = null,
        customerId: Int? = null,
        customerCode: String? = null,
        townshipId: Int? = null,
        saleTeamId: Int? = null,
        activeOnly: Boolean = true,
        page: Int = 1,
        limit: Int = 50
    ): Flow<NetworkResult<List<RemoteCustomer>>> = flow {
        emit(NetworkResult.Loading())

        val result = safeApiCall {
            apiService.getCustomers(search, customerId, customerCode, townshipId, saleTeamId, activeOnly, page, limit)
        }
        
        when (result) {
            is NetworkResult.Success -> {
                val customers = result.data.data ?: emptyList()
                emit(NetworkResult.Success(customers))
            }
            is NetworkResult.Error -> {
                emit(NetworkResult.Error(result.message, result.code))
            }
            is NetworkResult.Loading -> {
                // Already emitted loading state
            }
        }
    }
    
    /**
     * Search customers by name or code
     */
    fun searchCustomers(
        query: String,
        page: Int = 1,
        limit: Int = 50
    ): Flow<NetworkResult<List<RemoteCustomer>>> = flow {
        emit(NetworkResult.Loading())
        
        val result = safeApiCall {
            apiService.getCustomers(search = query, page = page, limit = limit)
        }
        
        when (result) {
            is NetworkResult.Success -> {
                val customers = result.data.data ?: emptyList()
                emit(NetworkResult.Success(customers))
            }
            is NetworkResult.Error -> {
                emit(NetworkResult.Error(result.message, result.code))
            }
            is NetworkResult.Loading -> {
                // Already emitted loading state
            }
        }
    }
    
    /**
     * Get customer by ID
     */
    suspend fun getCustomerById(customerId: Int): NetworkResult<RemoteCustomer?> {
        val result = safeApiCall {
            apiService.getCustomers(customerId = customerId)
        }
        
        return when (result) {
            is NetworkResult.Success -> {
                val customer = result.data.data?.firstOrNull()
                NetworkResult.Success(customer)
            }
            is NetworkResult.Error -> {
                NetworkResult.Error(result.message, result.code)
            }
            is NetworkResult.Loading -> {
                NetworkResult.Loading()
            }
        }
    }
    
    /**
     * Get customer by customer code
     */
    suspend fun getCustomerByCode(customerCode: String): NetworkResult<RemoteCustomer?> {
        val result = safeApiCall {
            apiService.getCustomers(customerCode = customerCode)
        }

        return when (result) {
            is NetworkResult.Success -> {
                val customer = result.data.data?.firstOrNull()
                NetworkResult.Success(customer)
            }
            is NetworkResult.Error -> {
                NetworkResult.Error(result.message, result.code)
            }
            is NetworkResult.Loading -> {
                NetworkResult.Loading()
            }
        }
    }

    /**
     * Get customers with location data (for map features)
     */
    fun getCustomersWithLocation(
        townshipId: Int? = null,
        limit: Int = 100
    ): Flow<NetworkResult<List<RemoteCustomer>>> = flow {
        emit(NetworkResult.Loading())

        val result = safeApiCall {
            apiService.getCustomers(
                townshipId = townshipId,
                activeOnly = true,
                limit = limit
            )
        }

        when (result) {
            is NetworkResult.Success -> {
                // Filter customers that have valid location data
                val customersWithLocation = result.data.data?.filter { customer ->
                    customer.latitude != null && customer.longitude != null &&
                    customer.latitude != 0.0 && customer.longitude != 0.0
                } ?: emptyList()

                emit(NetworkResult.Success(customersWithLocation))
            }
            is NetworkResult.Error -> {
                emit(NetworkResult.Error(result.message, result.code))
            }
            is NetworkResult.Loading -> {
                // Already emitted loading state
            }
        }
    }
}
