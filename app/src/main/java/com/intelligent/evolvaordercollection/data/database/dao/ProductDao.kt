package com.intelligent.evolvaordercollection.data.database.dao

import androidx.room.*
import com.intelligent.evolvaordercollection.data.model.Product
import kotlinx.coroutines.flow.Flow

@Dao
interface ProductDao {
    
    @Query("SELECT * FROM products WHERE isActive = 1 ORDER BY name ASC")
    fun getAllActiveProducts(): Flow<List<Product>>
    
    @Query("SELECT * FROM products ORDER BY name ASC")
    fun getAllProducts(): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE id = :id")
    suspend fun getProductById(id: Long): Product?
    
    @Query("SELECT * FROM products WHERE barcode = :barcode AND isActive = 1")
    suspend fun getProductByBarcode(barcode: String): Product?

    @Query("SELECT * FROM products WHERE productCode = :productCode AND isActive = 1")
    suspend fun getProductByProductCode(productCode: String): Product?

    @Query("SELECT * FROM products WHERE name LIKE '%' || :query || '%' OR category LIKE '%' || :query || '%' OR productCode LIKE '%' || :query || '%' OR barcode LIKE '%' || :query || '%' AND isActive = 1")
    fun searchProducts(query: String): Flow<List<Product>>
    
    @Query("SELECT DISTINCT category FROM products WHERE category != '' AND isActive = 1 ORDER BY category ASC")
    fun getCategories(): Flow<List<String>>
    
    @Query("SELECT * FROM products WHERE category = :category AND isActive = 1 ORDER BY name ASC")
    fun getProductsByCategory(category: String): Flow<List<Product>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: Product): Long
    
    @Update
    suspend fun updateProduct(product: Product)
    
    @Delete
    suspend fun deleteProduct(product: Product)
    
    @Query("UPDATE products SET isActive = 0 WHERE id = :id")
    suspend fun deactivateProduct(id: Long)
    
    @Query("UPDATE products SET stockQuantity = stockQuantity - :quantity WHERE id = :id")
    suspend fun reduceStock(id: Long, quantity: Int)
    
    @Query("UPDATE products SET stockQuantity = stockQuantity + :quantity WHERE id = :id")
    suspend fun increaseStock(id: Long, quantity: Int)

    @Query("SELECT COUNT(*) FROM products WHERE isActive = 1")
    suspend fun getTotalProductCount(): Int

    @Query("SELECT * FROM products ORDER BY name ASC")
    suspend fun getAllProductsList(): List<Product>
}
