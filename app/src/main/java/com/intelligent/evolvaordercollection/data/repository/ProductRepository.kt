package com.intelligent.evolvaordercollection.data.repository

import com.intelligent.evolvaordercollection.data.database.dao.ProductDao
import com.intelligent.evolvaordercollection.data.model.Product
import kotlinx.coroutines.flow.Flow

class ProductRepository(
    private val productDao: ProductDao
) {
    
    fun getAllActiveProducts(): Flow<List<Product>> = productDao.getAllActiveProducts()
    
    fun getAllProducts(): Flow<List<Product>> = productDao.getAllProducts()
    
    suspend fun getProductById(id: Long): Product? = productDao.getProductById(id)
    
    suspend fun getProductByBarcode(barcode: String): Product? = productDao.getProductByBarcode(barcode)

    suspend fun getProductByProductCode(productCode: String): Product? = productDao.getProductByProductCode(productCode)

    fun searchProducts(query: String): Flow<List<Product>> = productDao.searchProducts(query)
    
    fun getCategories(): Flow<List<String>> = productDao.getCategories()
    
    fun getProductsByCategory(category: String): Flow<List<Product>> = productDao.getProductsByCategory(category)
    
    suspend fun insertProduct(product: Product): Long = productDao.insertProduct(product)
    
    suspend fun updateProduct(product: Product) = productDao.updateProduct(product)
    
    suspend fun deleteProduct(product: Product) = productDao.deleteProduct(product)
    
    suspend fun deactivateProduct(id: Long) = productDao.deactivateProduct(id)
    
    suspend fun reduceStock(id: Long, quantity: Int) = productDao.reduceStock(id, quantity)
    
    suspend fun increaseStock(id: Long, quantity: Int) = productDao.increaseStock(id, quantity)

    suspend fun getTotalProductCount(): Int = productDao.getTotalProductCount()
}
