package com.intelligent.evolvaordercollection.data.network

import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * Network module for creating Retrofit instance and API services
 */
object NetworkModule {
    
    /**
     * Create OkHttp client with logging and timeouts
     */
    private fun createOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(ApiConfig.CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(ApiConfig.READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(ApiConfig.WRITE_TIMEOUT, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build()
    }
    
    /**
     * Create Gson converter with custom configurations
     */
    private fun createGsonConverter(): GsonConverterFactory {
        val gson = GsonBuilder()
            .setLenient()
            .create()
        
        return GsonConverterFactory.create(gson)
    }
    
    /**
     * Create Retrofit instance
     */
    private fun createRetrofit(): Retrofit {
        return Retrofit.Builder()
            .baseUrl(ApiConfig.BASE_URL)
            .client(createOkHttpClient())
            .addConverterFactory(createGsonConverter())
            .build()
    }
    
    /**
     * Provide ProductApiService instance
     */
    fun provideProductApiService(): ProductApiService {
        return createRetrofit().create(ProductApiService::class.java)
    }
}

/**
 * Extension functions for handling API responses
 */
suspend fun <T> safeApiCall(
    apiCall: suspend () -> retrofit2.Response<T>
): NetworkResult<T> {
    return try {
        println("Making API call...")
        val response = apiCall()
        println("API Response: Code=${response.code()}, Message=${response.message()}")

        if (response.isSuccessful) {
            response.body()?.let { body ->
                println("API Success: Response body received")
                NetworkResult.Success(body)
            } ?: run {
                println("API Error: Empty response body")
                NetworkResult.Error("Empty response body")
            }
        } else {
            val errorBody = response.errorBody()?.string()
            val errorMessage = "HTTP ${response.code()}: ${response.message()}" +
                if (errorBody != null) " - $errorBody" else ""
            println("API Error: $errorMessage")
            NetworkResult.Error(
                message = errorMessage,
                code = response.code()
            )
        }
    } catch (e: java.net.UnknownHostException) {
        val message = "Cannot connect to server. Please check your internet connection and server URL."
        println("Network Error: $message - ${e.message}")
        NetworkResult.Error(message = message)
    } catch (e: java.net.ConnectException) {
        val message = "Connection refused. Please check if the server is running."
        println("Connection Error: $message - ${e.message}")
        NetworkResult.Error(message = message)
    } catch (e: java.net.SocketTimeoutException) {
        val message = "Connection timeout. Please try again."
        println("Timeout Error: $message - ${e.message}")
        NetworkResult.Error(message = message)
    } catch (e: Exception) {
        val message = "Network error: ${e.message ?: "Unknown error occurred"}"
        println("General Error: $message")
        NetworkResult.Error(message = message)
    }
}
