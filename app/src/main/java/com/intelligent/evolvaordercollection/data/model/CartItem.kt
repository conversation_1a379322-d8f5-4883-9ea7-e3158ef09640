package com.intelligent.evolvaordercollection.data.model

import java.math.BigDecimal

data class CartItem(
    val product: Product,
    val quantity: Int,
    val discount: Double = 0.0 // Discount percentage (0-100)
) {
    val totalPrice: BigDecimal
        get() = calculateTotalPrice(product.price, quantity, discount)

    companion object {
        private fun calculateTotalPrice(unitPrice: BigDecimal, quantity: Int, discountPercent: Double): BigDecimal {
            val subtotal = unitPrice.multiply(BigDecimal(quantity))
            val discountAmount = subtotal.multiply(BigDecimal(discountPercent)).divide(BigDecimal(100))
            return subtotal.subtract(discountAmount)
        }
    }
}
