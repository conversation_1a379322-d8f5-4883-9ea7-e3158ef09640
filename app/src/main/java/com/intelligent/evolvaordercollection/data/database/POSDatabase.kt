package com.intelligent.evolvaordercollection.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.intelligent.evolvaordercollection.data.database.dao.*
import com.intelligent.evolvaordercollection.data.model.*

@Database(
    entities = [
        Product::class,
        Customer::class,
        Order::class,
        OrderItem::class
    ],
    version = 5,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class POSDatabase : RoomDatabase() {
    
    abstract fun productDao(): ProductDao
    abstract fun customerDao(): CustomerDao
    abstract fun orderDao(): OrderDao
    abstract fun orderItemDao(): OrderItemDao
    
    companion object {
        @Volatile
        private var INSTANCE: POSDatabase? = null

        // Migration from version 3 to 4: Add new customer fields
        private val MIGRATION_3_4 = object : Migration(3, 4) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add new columns to customers table
                database.execSQL("ALTER TABLE customers ADD COLUMN customerCode TEXT NOT NULL DEFAULT ''")
                database.execSQL("ALTER TABLE customers ADD COLUMN contactPerson TEXT NOT NULL DEFAULT ''")
                database.execSQL("ALTER TABLE customers ADD COLUMN creditLimit TEXT NOT NULL DEFAULT '0'")
            }
        }

        // Migration from version 4 to 5: Add location and township/saleteam fields
        private val MIGRATION_4_5 = object : Migration(4, 5) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add location and organizational fields to customers table
                database.execSQL("ALTER TABLE customers ADD COLUMN townshipId INTEGER")
                database.execSQL("ALTER TABLE customers ADD COLUMN townshipName TEXT NOT NULL DEFAULT ''")
                database.execSQL("ALTER TABLE customers ADD COLUMN saleTeamId INTEGER")
                database.execSQL("ALTER TABLE customers ADD COLUMN saleTeamName TEXT NOT NULL DEFAULT ''")
                database.execSQL("ALTER TABLE customers ADD COLUMN latitude REAL")
                database.execSQL("ALTER TABLE customers ADD COLUMN longitude REAL")
            }
        }
        
        fun getDatabase(context: Context): POSDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    POSDatabase::class.java,
                    "pos_database"
                )
                    .addMigrations(MIGRATION_3_4, MIGRATION_4_5)
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
