package com.intelligent.evolvaordercollection.data.repository

import com.intelligent.evolvaordercollection.data.database.dao.CustomerDao
import com.intelligent.evolvaordercollection.data.model.Customer
import kotlinx.coroutines.flow.Flow

class CustomerRepository(
    private val customerDao: CustomerDao
) {
    
    fun getAllActiveCustomers(): Flow<List<Customer>> = customerDao.getAllActiveCustomers()
    
    fun getAllCustomers(): Flow<List<Customer>> = customerDao.getAllCustomers()
    
    suspend fun getCustomerById(id: Long): Customer? = customerDao.getCustomerById(id)
    
    suspend fun getCustomerByPhone(phone: String): Customer? = customerDao.getCustomerByPhone(phone)
    
    suspend fun getCustomerByEmail(email: String): Customer? = customerDao.getCustomerByEmail(email)

    suspend fun getCustomerByCode(customerCode: String): Customer? = customerDao.getCustomerByCode(customerCode)
    
    fun searchCustomers(query: String): Flow<List<Customer>> = customerDao.searchCustomers(query)
    
    suspend fun insertCustomer(customer: Customer): Long = customerDao.insertCustomer(customer)
    
    suspend fun updateCustomer(customer: Customer) = customerDao.updateCustomer(customer)
    
    suspend fun deleteCustomer(customer: Customer) = customerDao.deleteCustomer(customer)
    
    suspend fun deactivateCustomer(id: Long) = customerDao.deactivateCustomer(id)

    suspend fun getAllCustomersList(): List<Customer> = customerDao.getAllCustomersList()
}
