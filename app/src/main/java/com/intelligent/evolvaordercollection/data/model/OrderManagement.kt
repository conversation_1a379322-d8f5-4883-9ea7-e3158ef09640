package com.intelligent.evolvaordercollection.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.math.BigDecimal

// Enhanced Order Status is now defined in Order.kt to avoid redeclaration

// Collection Agent/Driver model
@Entity(tableName = "collection_agents")
data class CollectionAgent(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val email: String = "",
    val phone: String = "",
    val vehicleType: String = "", // bike, car, truck, etc.
    val vehicleNumber: String = "",
    val isActive: Boolean = true,
    val currentLocation: String = "",
    val totalDeliveries: Int = 0,
    val rating: Float = 0.0f,
    val createdAt: Long = System.currentTimeMillis()
)

// Collection Point model
@Entity(tableName = "collection_points")
data class CollectionPoint(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val address: String,
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
    val contactPerson: String = "",
    val contactPhone: String = "",
    val operatingHours: String = "",
    val isActive: Boolean = true,
    val capacity: Int = 0,
    val currentLoad: Int = 0
)

// Order Assignment model
@Entity(tableName = "order_assignments")
data class OrderAssignment(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val orderId: Long,
    val agentId: Long,
    val collectionPointId: Long? = null,
    val assignedAt: Long = System.currentTimeMillis(),
    val estimatedCollectionTime: Long? = null,
    val actualCollectionTime: Long? = null,
    val status: AssignmentStatus = AssignmentStatus.ASSIGNED,
    val notes: String = ""
)

enum class AssignmentStatus {
    ASSIGNED,
    ACCEPTED,
    IN_PROGRESS,
    COMPLETED,
    CANCELLED
}

// Order Tracking model
@Entity(tableName = "order_tracking")
data class OrderTracking(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val orderId: Long,
    val status: OrderStatus,
    val location: String = "",
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
    val timestamp: Long = System.currentTimeMillis(),
    val notes: String = "",
    val agentId: Long? = null
)

// Order Alert model
@Entity(tableName = "order_alerts")
data class OrderAlert(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val orderId: Long,
    val alertType: AlertType,
    val title: String,
    val message: String,
    val priority: AlertPriority = AlertPriority.MEDIUM,
    val isRead: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val expiresAt: Long? = null
)

enum class AlertType {
    ORDER_DELAYED,
    AGENT_UNAVAILABLE,
    COLLECTION_POINT_FULL,
    CUSTOMER_COMPLAINT,
    SYSTEM_ERROR,
    PAYMENT_ISSUE,
    INVENTORY_LOW,
    DELIVERY_FAILED
}

enum class AlertPriority {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}

// Performance Metrics model
data class PerformanceMetrics(
    val totalOrders: Int = 0,
    val successOrders: Int = 0,
    val waitingOrders: Int = 0,
    val rejectedOrders: Int = 0,
    val averageCollectionTime: Long = 0, // in minutes
    val successRate: Float = 0.0f, // percentage
    val totalRevenue: BigDecimal = BigDecimal.ZERO,
    val averageOrderValue: BigDecimal = BigDecimal.ZERO,
    val topPerformingAgent: String = "",
    val topPerformingProduct: String = "",
    val peakHours: List<Int> = emptyList(),
    val customerSatisfactionRate: Float = 0.0f
)

// Dashboard Widget Configuration
data class DashboardWidget(
    val id: String,
    val title: String,
    val type: WidgetType,
    val position: Int,
    val isVisible: Boolean = true,
    val size: WidgetSize = WidgetSize.MEDIUM,
    val refreshInterval: Long = 30000, // 30 seconds
    val configuration: Map<String, Any> = emptyMap()
)

enum class WidgetType {
    ORDER_STATUS_OVERVIEW,
    RECENT_ORDERS,
    PERFORMANCE_METRICS,
    AGENT_TRACKING,
    COLLECTION_POINTS,
    ALERTS_NOTIFICATIONS,
    REVENUE_CHART,
    ORDER_TRENDS,
    TOP_PRODUCTS,
    CUSTOMER_INSIGHTS,
    MAP_VIEW,
    QUICK_ACTIONS
}

enum class WidgetSize {
    SMALL,
    MEDIUM,
    LARGE,
    EXTRA_LARGE
}

// Filter and Search Configuration
data class OrderFilter(
    val status: List<OrderStatus> = emptyList(),
    val dateRange: DateRange? = null,
    val customerId: Long? = null,
    val agentId: Long? = null,
    val collectionPointId: Long? = null,
    val minAmount: BigDecimal? = null,
    val maxAmount: BigDecimal? = null,
    val searchQuery: String = ""
)

data class DateRange(
    val startDate: Long,
    val endDate: Long
)

// Export Configuration
data class ExportConfig(
    val format: ExportFormat,
    val includeHeaders: Boolean = true,
    val dateRange: DateRange? = null,
    val filters: OrderFilter? = null,
    val columns: List<String> = emptyList()
)

enum class ExportFormat {
    CSV,
    EXCEL,
    PDF,
    JSON
}
