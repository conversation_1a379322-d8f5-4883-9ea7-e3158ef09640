package com.intelligent.evolvaordercollection.data.repository

import android.content.Context
import android.os.Environment
import com.intelligent.evolvaordercollection.data.database.POSDatabase
import com.intelligent.evolvaordercollection.data.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.File
import java.io.FileOutputStream
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

class ExportRepository(
    private val database: POSDatabase,
    private val context: Context
) {
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    
    // Export all data to CSV files
    suspend fun exportToCSV(): Result<ExportResult> = withContext(Dispatchers.IO) {
        try {
            val timestamp = dateFormat.format(Date())
            val exportDir = File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), "POS_Exports")
            
            if (!exportDir.exists()) {
                exportDir.mkdirs()
            }
            
            val exportFolder = File(exportDir, "Export_$timestamp")
            exportFolder.mkdirs()
            
            // Export products
            val products = database.productDao().getAllProductsList()
            val productsFile = File(exportFolder, "products.csv")
            FileWriter(productsFile).use { writer ->
                writer.write(productsToCsv(products))
            }

            // Export customers
            val customers = database.customerDao().getAllCustomersList()
            val customersFile = File(exportFolder, "customers.csv")
            FileWriter(customersFile).use { writer ->
                writer.write(customersToCsv(customers))
            }

            // Export orders
            val orders = database.orderDao().getAllOrdersList()
            val ordersFile = File(exportFolder, "orders.csv")
            FileWriter(ordersFile).use { writer ->
                writer.write(ordersToCsv(orders))
            }

            // Export order items - get all items from all orders
            val orderItems = mutableListOf<OrderItem>()
            orders.forEach { order ->
                val items = database.orderItemDao().getOrderItemsList(order.id)
                orderItems.addAll(items)
            }
            val orderItemsFile = File(exportFolder, "order_items.csv")
            FileWriter(orderItemsFile).use { writer ->
                writer.write(orderItemsToCsv(orderItems))
            }

            // Create summary file
            val summaryFile = File(exportFolder, "export_summary.txt")
            FileWriter(summaryFile).use { writer ->
                writer.write(createExportSummary(products.size, customers.size, orders.size, orderItems.size, timestamp))
            }
            
            Result.success(
                ExportResult(
                    exportPath = exportFolder.absolutePath,
                    fileCount = 5,
                    totalRecords = products.size + customers.size + orders.size + orderItems.size,
                    exportFormat = ExportFormat.CSV,
                    timestamp = System.currentTimeMillis()
                )
            )
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Export all data to Excel file
    suspend fun exportToExcel(): Result<ExportResult> = withContext(Dispatchers.IO) {
        try {
            val timestamp = dateFormat.format(Date())
            val exportDir = File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), "POS_Exports")
            
            if (!exportDir.exists()) {
                exportDir.mkdirs()
            }
            
            val excelFile = File(exportDir, "POS_Export_$timestamp.xlsx")
            val workbook = XSSFWorkbook()
            
            // Create products sheet
            val products = database.productDao().getAllProductsList()
            val productsSheet = workbook.createSheet("Products")
            createProductsSheet(productsSheet, products)

            // Create customers sheet
            val customers = database.customerDao().getAllCustomersList()
            val customersSheet = workbook.createSheet("Customers")
            createCustomersSheet(customersSheet, customers)

            // Create orders sheet
            val orders = database.orderDao().getAllOrdersList()
            val ordersSheet = workbook.createSheet("Orders")
            createOrdersSheet(ordersSheet, orders)

            // Create order items sheet
            val orderItems = mutableListOf<OrderItem>()
            orders.forEach { order ->
                val items = database.orderItemDao().getOrderItemsList(order.id)
                orderItems.addAll(items)
            }
            val orderItemsSheet = workbook.createSheet("Order Items")
            createOrderItemsSheet(orderItemsSheet, orderItems)

            // Create summary sheet
            val summarySheet = workbook.createSheet("Summary")
            createSummarySheet(summarySheet, products.size, customers.size, orders.size, orderItems.size, timestamp)
            
            // Write to file
            FileOutputStream(excelFile).use { outputStream ->
                workbook.write(outputStream)
            }
            workbook.close()
            
            Result.success(
                ExportResult(
                    exportPath = excelFile.absolutePath,
                    fileCount = 1,
                    totalRecords = products.size + customers.size + orders.size + orderItems.size,
                    exportFormat = ExportFormat.EXCEL,
                    timestamp = System.currentTimeMillis()
                )
            )
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Export specific date range
    suspend fun exportOrdersByDateRange(startDate: Long, endDate: Long, format: ExportFormat): Result<ExportResult> = withContext(Dispatchers.IO) {
        try {
            val orders = database.orderDao().getOrdersByDateRangeList(startDate, endDate)
            val orderItems = mutableListOf<OrderItem>()
            orders.forEach { order ->
                val items = database.orderItemDao().getOrderItemsList(order.id)
                orderItems.addAll(items)
            }
            
            val timestamp = dateFormat.format(Date())
            val exportDir = File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), "POS_Exports")
            
            if (!exportDir.exists()) {
                exportDir.mkdirs()
            }
            
            when (format) {
                ExportFormat.CSV -> {
                    val exportFolder = File(exportDir, "Orders_Export_$timestamp")
                    exportFolder.mkdirs()
                    
                    val ordersFile = File(exportFolder, "orders.csv")
                    FileWriter(ordersFile).use { writer ->
                        writer.write(ordersToCsv(orders))
                    }

                    val orderItemsFile = File(exportFolder, "order_items.csv")
                    FileWriter(orderItemsFile).use { writer ->
                        writer.write(orderItemsToCsv(orderItems))
                    }
                    
                    Result.success(
                        ExportResult(
                            exportPath = exportFolder.absolutePath,
                            fileCount = 2,
                            totalRecords = orders.size + orderItems.size,
                            exportFormat = format,
                            timestamp = System.currentTimeMillis()
                        )
                    )
                }
                
                ExportFormat.EXCEL -> {
                    val excelFile = File(exportDir, "Orders_Export_$timestamp.xlsx")
                    val workbook = XSSFWorkbook()
                    
                    val ordersSheet = workbook.createSheet("Orders")
                    createOrdersSheet(ordersSheet, orders)
                    
                    val orderItemsSheet = workbook.createSheet("Order Items")
                    createOrderItemsSheet(orderItemsSheet, orderItems)
                    
                    FileOutputStream(excelFile).use { outputStream ->
                        workbook.write(outputStream)
                    }
                    workbook.close()
                    
                    Result.success(
                        ExportResult(
                            exportPath = excelFile.absolutePath,
                            fileCount = 1,
                            totalRecords = orders.size + orderItems.size,
                            exportFormat = format,
                            timestamp = System.currentTimeMillis()
                        )
                    )
                }
            }
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Helper functions for Excel sheet creation
    private fun createProductsSheet(sheet: org.apache.poi.ss.usermodel.Sheet, products: List<Product>) {
        val headerRow = sheet.createRow(0)
        val headers = arrayOf("ID", "Name", "Description", "Price", "Category", "Product Code", "Barcode", "Stock", "Active", "Created", "Updated")
        
        headers.forEachIndexed { index, header ->
            headerRow.createCell(index).setCellValue(header)
        }
        
        products.forEachIndexed { index, product ->
            val row = sheet.createRow(index + 1)
            row.createCell(0).setCellValue(product.id.toDouble())
            row.createCell(1).setCellValue(product.name)
            row.createCell(2).setCellValue(product.description)
            row.createCell(3).setCellValue(product.price.toDouble())
            row.createCell(4).setCellValue(product.category)
            row.createCell(5).setCellValue(product.productCode)
            row.createCell(6).setCellValue(product.barcode)
            row.createCell(7).setCellValue(product.stockQuantity.toDouble())
            row.createCell(8).setCellValue(product.isActive)
            row.createCell(9).setCellValue(Date(product.createdAt))
            row.createCell(10).setCellValue(Date(product.updatedAt))
        }
        
        // Auto-size columns
        for (i in 0 until headers.size) {
            sheet.autoSizeColumn(i)
        }
    }
    
    private fun createCustomersSheet(sheet: org.apache.poi.ss.usermodel.Sheet, customers: List<Customer>) {
        val headerRow = sheet.createRow(0)
        val headers = arrayOf("ID", "Name", "Email", "Phone", "Address", "Active", "Created", "Updated")
        
        headers.forEachIndexed { index, header ->
            headerRow.createCell(index).setCellValue(header)
        }
        
        customers.forEachIndexed { index, customer ->
            val row = sheet.createRow(index + 1)
            row.createCell(0).setCellValue(customer.id.toDouble())
            row.createCell(1).setCellValue(customer.name)
            row.createCell(2).setCellValue(customer.email)
            row.createCell(3).setCellValue(customer.phone)
            row.createCell(4).setCellValue(customer.address)
            row.createCell(5).setCellValue(customer.isActive)
            row.createCell(6).setCellValue(Date(customer.createdAt))
            row.createCell(7).setCellValue(Date(customer.updatedAt))
        }
        
        for (i in 0 until headers.size) {
            sheet.autoSizeColumn(i)
        }
    }
    
    private fun createOrdersSheet(sheet: org.apache.poi.ss.usermodel.Sheet, orders: List<Order>) {
        val headerRow = sheet.createRow(0)
        val headers = arrayOf("ID", "Customer ID", "Customer Name", "Total Amount", "Status", "Notes", "Created", "Updated")
        
        headers.forEachIndexed { index, header ->
            headerRow.createCell(index).setCellValue(header)
        }
        
        orders.forEachIndexed { index, order ->
            val row = sheet.createRow(index + 1)
            row.createCell(0).setCellValue(order.id.toDouble())
            row.createCell(1).setCellValue((order.customerId ?: 0L).toDouble())
            row.createCell(2).setCellValue(order.customerName)
            row.createCell(3).setCellValue(order.totalAmount.toDouble())
            row.createCell(4).setCellValue(order.status.name)
            row.createCell(5).setCellValue(order.notes)
            row.createCell(6).setCellValue(Date(order.createdAt))
            row.createCell(7).setCellValue(Date(order.updatedAt))
        }
        
        for (i in 0 until headers.size) {
            sheet.autoSizeColumn(i)
        }
    }
    
    private fun createOrderItemsSheet(sheet: org.apache.poi.ss.usermodel.Sheet, orderItems: List<OrderItem>) {
        val headerRow = sheet.createRow(0)
        val headers = arrayOf("ID", "Order ID", "Product ID", "Product Name", "Quantity", "Unit Price", "Total Price", "Notes")

        headers.forEachIndexed { index, header ->
            headerRow.createCell(index).setCellValue(header)
        }

        orderItems.forEachIndexed { index, item ->
            val row = sheet.createRow(index + 1)
            row.createCell(0).setCellValue(item.id.toDouble())
            row.createCell(1).setCellValue(item.orderId.toDouble())
            row.createCell(2).setCellValue(item.productId.toDouble())
            row.createCell(3).setCellValue(item.productName)
            row.createCell(4).setCellValue(item.quantity.toDouble())
            row.createCell(5).setCellValue(item.unitPrice.toDouble())
            row.createCell(6).setCellValue(item.totalPrice.toDouble())
            row.createCell(7).setCellValue(item.notes)
        }

        for (i in 0 until headers.size) {
            sheet.autoSizeColumn(i)
        }
    }
    
    private fun createSummarySheet(sheet: org.apache.poi.ss.usermodel.Sheet, productsCount: Int, customersCount: Int, ordersCount: Int, orderItemsCount: Int, timestamp: String) {
        val summaryData = listOf(
            "POS Data Export Summary" to "",
            "Export Date" to timestamp,
            "Device ID" to generateDeviceId(context),
            "" to "",
            "Data Counts:" to "",
            "Products" to productsCount.toString(),
            "Customers" to customersCount.toString(),
            "Orders" to ordersCount.toString(),
            "Order Items" to orderItemsCount.toString(),
            "Total Records" to (productsCount + customersCount + ordersCount + orderItemsCount).toString()
        )
        
        summaryData.forEachIndexed { index, (label, value) ->
            val row = sheet.createRow(index)
            row.createCell(0).setCellValue(label)
            row.createCell(1).setCellValue(value)
        }
        
        sheet.autoSizeColumn(0)
        sheet.autoSizeColumn(1)
    }
    
    private fun createExportSummary(productsCount: Int, customersCount: Int, ordersCount: Int, orderItemsCount: Int, timestamp: String): String {
        return """
            POS Data Export Summary
            =======================
            
            Export Date: $timestamp
            Device ID: ${generateDeviceId(context)}
            
            Data Counts:
            - Products: $productsCount
            - Customers: $customersCount
            - Orders: $ordersCount
            - Order Items: $orderItemsCount
            
            Total Records: ${productsCount + customersCount + ordersCount + orderItemsCount}
            
            Files Exported:
            - products.csv
            - customers.csv
            - orders.csv
            - order_items.csv
            - export_summary.txt
        """.trimIndent()
    }
}

// Function for OrderItem CSV export
fun orderItemsToCsv(orderItems: List<OrderItem>): String {
    val header = "ID,Order ID,Product ID,Product Name,Quantity,Unit Price,Total Price,Notes"
    val rows = orderItems.map { item ->
        "${item.id},${item.orderId},${item.productId},\"${item.productName}\"," +
        "${item.quantity},${item.unitPrice},${item.totalPrice},\"${item.notes}\""
    }
    return listOf(header).plus(rows).joinToString("\n")
}

data class ExportResult(
    val exportPath: String,
    val fileCount: Int,
    val totalRecords: Int,
    val exportFormat: ExportFormat,
    val timestamp: Long
)

enum class ExportFormat {
    CSV, EXCEL
}
