package com.intelligent.evolvaordercollection.data.network

import com.intelligent.evolvaordercollection.data.model.ProductsResponse
import com.intelligent.evolvaordercollection.data.model.CustomersResponse
import com.intelligent.evolvaordercollection.data.model.TownshipsResponse
import com.intelligent.evolvaordercollection.data.model.SaleTeamsResponse
import com.intelligent.evolvaordercollection.data.model.CreateOrderRequest
import com.intelligent.evolvaordercollection.data.model.CreateOrderResponse
import retrofit2.Response
import retrofit2.http.*

/**
 * API service interface for product operations
 */
interface ProductApiService {
    
    /**
     * Get all products with optional filters
     */
    @GET("products/products.php")
    suspend fun getProducts(
        @Query("search") search: String? = null,
        @Query("category_id") categoryId: Int? = null,
        @Query("principal_id") principalId: Int? = null,
        @Query("active_only") activeOnly: Boolean = true,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 50
    ): Response<ProductsResponse>
    
    /**
     * Get products by category
     */
    @GET("products/products.php")
    suspend fun getProductsByCategory(
        @Query("category_id") categoryId: Int,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 50
    ): Response<ProductsResponse>

    /**
     * Get products by principal
     */
    @GET("products/products.php")
    suspend fun getProductsByPrincipal(
        @Query("principal_id") principalId: Int,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 50
    ): Response<ProductsResponse>

    /**
     * Search products by name or code
     */
    @GET("products/products.php")
    suspend fun searchProducts(
        @Query("search") query: String,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 50
    ): Response<ProductsResponse>

    /**
     * Get product by ID
     */
    @GET("products/products.php")
    suspend fun getProductById(
        @Query("product_id") productId: Int
    ): Response<ProductsResponse>

    /**
     * Get product by product code
     */
    @GET("products/products.php")
    suspend fun getProductByCode(
        @Query("product_code") productCode: String
    ): Response<ProductsResponse>

    /**
     * Get all customers with optional filters
     */
    @GET("customers/customers.php")
    suspend fun getCustomers(
        @Query("search") search: String? = null,
        @Query("customer_id") customerId: Int? = null,
        @Query("customer_code") customerCode: String? = null,
        @Query("township_id") townshipId: Int? = null,
        @Query("sale_team_id") saleTeamId: Int? = null,
        @Query("active_only") activeOnly: Boolean = true,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 50
    ): Response<CustomersResponse>

    /**
     * Get all townships
     */
    @GET("townships/townships.php")
    suspend fun getTownships(): Response<TownshipsResponse>

    /**
     * Get all sale teams
     */
    @GET("saleteams/saleteams.php")
    suspend fun getSaleTeams(): Response<SaleTeamsResponse>

    /**
     * Create order - Insert to OrderRequest & OrderRequestDetails
     */
    @POST("orders/create_order.php")
    suspend fun createOrder(@Body orderRequest: CreateOrderRequest): Response<CreateOrderResponse>
}

/**
 * Network result wrapper
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val message: String, val code: Int? = null) : NetworkResult<T>()
    data class Loading<T>(val isLoading: Boolean = true) : NetworkResult<T>()
}

/**
 * API endpoints configuration
 */
object ApiConfig {
    // Your server API URL - make sure you upload the API files here
    const val BASE_URL = "https://www.intelligentmyanmar.com/OrderCollectionApp(DemoROasis)/api/"
    const val IMAGE_BASE_URL = "https://www.intelligentmyanmar.com/OrderCollectionApp(DemoROasis)/images/"

    // For testing connectivity, you can temporarily use this:
    // const val BASE_URL = "https://jsonplaceholder.typicode.com/"

    // Connection timeout in seconds
    const val CONNECT_TIMEOUT = 60L // Increased timeout for testing
    const val READ_TIMEOUT = 60L
    const val WRITE_TIMEOUT = 60L

    // Test endpoints
    const val TEST_ENDPOINT = "simple_test.php"
    const val PRODUCTS_ENDPOINT = "products.php"
}
