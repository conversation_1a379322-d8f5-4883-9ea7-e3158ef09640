package com.intelligent.evolvaordercollection.data.model

import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

/**
 * Remote product model that matches the MySQL database structure
 */
data class RemoteProduct(
    @SerializedName("ProductID")
    val productId: Int,
    
    @SerializedName("Product Code")
    val productCode: String,
    
    @SerializedName("ProductName")
    val productName: String,
    
    @SerializedName("Product Description")
    val productDescription: String?,
    
    @SerializedName("CategoryID")
    val categoryId: String?, // Handle as String to avoid null issues

    @SerializedName("CategoryName")
    val categoryName: String?,

    @SerializedName("PrincipalID")
    val principalId: String?, // Handle as String to avoid null issues
    
    @SerializedName("PrincipalName")
    val principalName: String?,
    
    @SerializedName("Unit Size")
    val unitSize: String?,
    
    @SerializedName("Selling Price")
    val sellingPrice: Double,

    @SerializedName("Current Price")
    val currentPrice: Double,

    @SerializedName("Price Description")
    val priceDescription: String?,
    
    @SerializedName("ProductImagePath")
    val productImagePath: String?,
    
    @SerializedName("UnitsInStock")
    val unitsInStock: String, // Handle as String to avoid parsing issues
    
    @SerializedName("Discontinued")
    val discontinued: Int
) {
    /**
     * Convert remote product to local product model
     */
    fun toLocalProduct(): Product {
        val imageUrl = if (!productImagePath.isNullOrBlank()) {
            "https://www.intelligentmyanmar.com/OrderCollectionApp(DemoROasis)/images/$productImagePath"
        } else {
            ""
        }

        return Product(
            id = 0, // Let Room auto-generate the ID
            name = productName,
            description = productDescription ?: "",
            price = BigDecimal.valueOf(currentPrice), // Use current price from PriceList
            category = categoryName ?: "Uncategorized",
            productCode = productCode,
            barcode = productCode, // Using product code as barcode fallback
            stockQuantity = try { unitsInStock.toDoubleOrNull()?.toInt() ?: 0 } catch (e: Exception) { 0 },
            imageUrl = imageUrl,
            isActive = discontinued == 0, // 0 = active, 1 = discontinued
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * Get the full image URL
     */
    fun getImageUrl(): String {
        return if (!productImagePath.isNullOrBlank()) {
            "https://www.intelligentmyanmar.com/OrderCollectionApp(DemoROasis)/images/$productImagePath"
        } else {
            ""
        }
    }
    
    /**
     * Check if product has image
     */
    fun hasImage(): Boolean {
        return !productImagePath.isNullOrBlank()
    }
    
    /**
     * Get formatted price
     */
    fun getFormattedPrice(): String {
        return "MMK ${String.format("%,.0f", currentPrice)}"
    }
    
    /**
     * Get stock status
     */
    fun getStockStatus(): StockStatus {
        val stock = unitsInStock.toDoubleOrNull() ?: 0.0
        return when {
            discontinued == 1 -> StockStatus.DISCONTINUED
            stock <= 0 -> StockStatus.OUT_OF_STOCK
            stock <= 10 -> StockStatus.LOW_STOCK
            else -> StockStatus.IN_STOCK
        }
    }
}

enum class StockStatus {
    IN_STOCK,
    LOW_STOCK,
    OUT_OF_STOCK,
    DISCONTINUED
}

/**
 * API Response wrapper for products
 */
data class ProductsResponse(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: List<RemoteProduct>?,
    
    @SerializedName("total")
    val total: Int?
)

/**
 * API Request for product search/filter
 */
data class ProductRequest(
    val search: String? = null,
    val categoryId: Int? = null,
    val principalId: Int? = null,
    val activeOnly: Boolean = true,
    val page: Int = 1,
    val limit: Int = 50
)
