package com.intelligent.evolvaordercollection.data.repository

import com.intelligent.evolvaordercollection.data.database.dao.OrderDao
import com.intelligent.evolvaordercollection.data.database.dao.OrderItemDao
import com.intelligent.evolvaordercollection.data.model.Order
import com.intelligent.evolvaordercollection.data.model.OrderItem
import com.intelligent.evolvaordercollection.data.model.OrderStatus
import kotlinx.coroutines.flow.Flow
import java.math.BigDecimal

class OrderRepository(
    private val orderDao: OrderDao,
    private val orderItemDao: OrderItemDao
) {
    
    fun getAllOrders(): Flow<List<Order>> = orderDao.getAllOrders()
    
    fun getOrdersByStatus(status: OrderStatus): Flow<List<Order>> = orderDao.getOrdersByStatus(status)
    
    suspend fun getOrderById(id: Long): Order? = orderDao.getOrderById(id)
    
    suspend fun getOrderByNumber(orderNumber: String): Order? = orderDao.getOrderByNumber(orderNumber)
    
    fun getOrdersByCustomer(customerId: Long): Flow<List<Order>> = orderDao.getOrdersByCustomer(customerId)
    
    fun getTodayOrders(): Flow<List<Order>> = orderDao.getTodayOrders()
    
    fun getOrdersByDateRange(startDate: Long, endDate: Long): Flow<List<Order>> = 
        orderDao.getOrdersByDateRange(startDate, endDate)
    
    fun getOrderItems(orderId: Long): Flow<List<OrderItem>> = orderItemDao.getOrderItems(orderId)
    
    suspend fun insertOrder(order: Order): Long = orderDao.insertOrder(order)
    
    suspend fun updateOrder(order: Order) = orderDao.updateOrder(order)
    
    suspend fun deleteOrder(order: Order) = orderDao.deleteOrder(order)
    
    suspend fun updateOrderStatus(id: Long, status: OrderStatus) = 
        orderDao.updateOrderStatus(id, status)
    
    suspend fun insertOrderItem(orderItem: OrderItem): Long = orderItemDao.insertOrderItem(orderItem)
    
    suspend fun insertOrderItems(orderItems: List<OrderItem>) = orderItemDao.insertOrderItems(orderItems)
    
    suspend fun updateOrderItem(orderItem: OrderItem) = orderItemDao.updateOrderItem(orderItem)
    
    suspend fun deleteOrderItem(orderItem: OrderItem) = orderItemDao.deleteOrderItem(orderItem)
    
    suspend fun deleteOrderItems(orderId: Long) = orderItemDao.deleteOrderItems(orderId)
    
    suspend fun getTodayOrderCount(): Int = orderDao.getTodayOrderCount()
    
    suspend fun getTodayOrderTotal(): BigDecimal = 
        BigDecimal(orderDao.getTodayOrderTotal())
    
    suspend fun createOrderWithItems(order: Order, items: List<OrderItem>): Long {
        val orderId = insertOrder(order)
        val orderItemsWithOrderId = items.map { it.copy(orderId = orderId) }
        insertOrderItems(orderItemsWithOrderId)
        return orderId
    }
}
