package com.intelligent.evolvaordercollection.data.database.dao

import androidx.room.*
import com.intelligent.evolvaordercollection.data.model.Order
import com.intelligent.evolvaordercollection.data.model.OrderStatus
import kotlinx.coroutines.flow.Flow

@Dao
interface OrderDao {
    
    @Query("SELECT * FROM orders ORDER BY createdAt DESC")
    fun getAllOrders(): Flow<List<Order>>

    @Query("SELECT * FROM orders WHERE status = :status ORDER BY createdAt DESC")
    fun getOrdersByStatus(status: OrderStatus): Flow<List<Order>>

    @Query("SELECT * FROM orders WHERE id = :id")
    suspend fun getOrderById(id: Long): Order?

    @Query("SELECT * FROM orders WHERE orderNumber = :orderNumber")
    suspend fun getOrderByNumber(orderNumber: String): Order?

    @Query("SELECT * FROM orders WHERE customerId = :customerId ORDER BY createdAt DESC")
    fun getOrdersByCustomer(customerId: Long): Flow<List<Order>>

    @Query("SELECT * FROM orders WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now') ORDER BY createdAt DESC")
    fun getTodayOrders(): Flow<List<Order>>

    @Query("SELECT * FROM orders WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    fun getOrdersByDateRange(startDate: Long, endDate: Long): Flow<List<Order>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrder(order: Order): Long
    
    @Update
    suspend fun updateOrder(order: Order)
    
    @Delete
    suspend fun deleteOrder(order: Order)
    
    @Query("UPDATE orders SET status = :status, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateOrderStatus(id: Long, status: OrderStatus, updatedAt: Long = System.currentTimeMillis())
    
    @Query("SELECT COUNT(*) FROM orders WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now')")
    suspend fun getTodayOrderCount(): Int
    
    @Query("SELECT COALESCE(SUM(totalAmount), 0) FROM orders WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now') AND status = 'COMPLETED'")
    suspend fun getTodayOrderTotal(): String

    @Query("SELECT * FROM orders ORDER BY createdAt DESC")
    suspend fun getAllOrdersList(): List<Order>

    @Query("SELECT * FROM orders WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    suspend fun getOrdersByDateRangeList(startDate: Long, endDate: Long): List<Order>
}
