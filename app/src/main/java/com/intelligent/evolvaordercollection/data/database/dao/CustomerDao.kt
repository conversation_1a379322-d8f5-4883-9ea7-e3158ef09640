package com.intelligent.evolvaordercollection.data.database.dao

import androidx.room.*
import com.intelligent.evolvaordercollection.data.model.Customer
import kotlinx.coroutines.flow.Flow

@Dao
interface CustomerDao {
    
    @Query("SELECT * FROM customers WHERE isActive = 1 ORDER BY name ASC")
    fun getAllActiveCustomers(): Flow<List<Customer>>
    
    @Query("SELECT * FROM customers ORDER BY name ASC")
    fun getAllCustomers(): Flow<List<Customer>>
    
    @Query("SELECT * FROM customers WHERE id = :id")
    suspend fun getCustomerById(id: Long): Customer?
    
    @Query("SELECT * FROM customers WHERE phone = :phone AND isActive = 1")
    suspend fun getCustomerByPhone(phone: String): Customer?
    
    @Query("SELECT * FROM customers WHERE email = :email AND isActive = 1")
    suspend fun getCustomerByEmail(email: String): Customer?

    @Query("SELECT * FROM customers WHERE customerCode = :customerCode")
    suspend fun getCustomerByCode(customerCode: String): Customer?
    
    @Query("SELECT * FROM customers WHERE name LIKE '%' || :query || '%' OR phone LIKE '%' || :query || '%' OR email LIKE '%' || :query || '%' AND isActive = 1")
    fun searchCustomers(query: String): Flow<List<Customer>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomer(customer: Customer): Long
    
    @Update
    suspend fun updateCustomer(customer: Customer)
    
    @Delete
    suspend fun deleteCustomer(customer: Customer)
    
    @Query("UPDATE customers SET isActive = 0 WHERE id = :id")
    suspend fun deactivateCustomer(id: Long)

    @Query("SELECT * FROM customers WHERE isActive = 1 ORDER BY name ASC")
    suspend fun getAllCustomersList(): List<Customer>
}
