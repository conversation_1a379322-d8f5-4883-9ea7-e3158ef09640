package com.intelligent.evolvaordercollection.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "customers")
data class Customer(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val email: String = "",
    val phone: String = "",
    val address: String = "",
    val customerCode: String = "",
    val contactPerson: String = "",
    val creditLimit: java.math.BigDecimal = java.math.BigDecimal.ZERO,
    val townshipId: Int? = null,
    val townshipName: String = "",
    val saleTeamId: Int? = null,
    val saleTeamName: String = "",
    val latitude: Double? = null,
    val longitude: Double? = null,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Check if customer has valid location coordinates
     */
    fun hasLocation(): Boolean {
        return latitude != null && longitude != null &&
               latitude != 0.0 && longitude != 0.0
    }

    /**
     * Get Google Maps URL for customer location with customer name as label
     */
    fun getMapUrl(): String? {
        return if (hasLocation()) {
            val encodedName = java.net.URLEncoder.encode(name, "UTF-8")
            "https://www.google.com/maps/place/$encodedName/@$latitude,$longitude,17z"
        } else null
    }
}
