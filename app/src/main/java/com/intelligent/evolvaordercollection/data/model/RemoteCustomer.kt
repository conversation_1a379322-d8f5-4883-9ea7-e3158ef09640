package com.intelligent.evolvaordercollection.data.model

import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

/**
 * Remote customer model that matches the MySQL database structure
 */
data class RemoteCustomer(
    @SerializedName("CustomerID")
    val customerId: Int,
    
    @SerializedName("CustomerCode")
    val customerCode: String,
    
    @SerializedName("CompanyName")
    val companyName: String,
    
    @SerializedName("ContactPerson1")
    val contactPerson1: String?,
    
    @SerializedName("ContactPerson2")
    val contactPerson2: String?,
    
    @SerializedName("Address")
    val address: String?,
    
    @SerializedName("Phone")
    val phone: String?,
    
    @SerializedName("MobileNo")
    val mobileNo: String?,
    
    @SerializedName("EmailAddress")
    val emailAddress: String?,
    
    @SerializedName("CreditLimit")
    val creditLimit: Double,
    
    @SerializedName("Active")
    val active: Int,
    
    @SerializedName("TownshipID")
    val townshipId: Int?,

    @SerializedName("TownshipName")
    val townshipName: String?,

    @SerializedName("SubZoneID")
    val subZoneId: Int?,

    @SerializedName("SaleTeamID")
    val saleTeamId: Int?,

    @SerializedName("SaleTeam")
    val saleTeam: String?,
    
    @SerializedName("Latitude")
    val latitude: Double?,
    
    @SerializedName("Longitude")
    val longitude: Double?,
    
    @SerializedName("CreationDate")
    val creationDate: String?,
    
    @SerializedName("ModifiedDate")
    val modifiedDate: String?
) {
    /**
     * Convert remote customer to local customer model
     */
    fun toLocalCustomer(): Customer {
        return Customer(
            id = 0, // Let Room auto-generate the ID
            name = companyName,
            email = emailAddress ?: "",
            phone = phone ?: "",
            address = address ?: "",
            customerCode = customerCode,
            contactPerson = contactPerson1 ?: "",
            creditLimit = BigDecimal.valueOf(creditLimit),
            townshipId = townshipId,
            townshipName = townshipName ?: "",
            saleTeamId = saleTeamId,
            saleTeamName = saleTeam ?: "",
            latitude = latitude,
            longitude = longitude,
            isActive = active == 1,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * Get primary contact information
     */
    fun getPrimaryContact(): String {
        return when {
            !contactPerson1.isNullOrBlank() -> contactPerson1
            !contactPerson2.isNullOrBlank() -> contactPerson2
            else -> "No contact person"
        }
    }
    
    /**
     * Get primary phone number
     */
    fun getPrimaryPhone(): String {
        return when {
            !mobileNo.isNullOrBlank() -> mobileNo
            !phone.isNullOrBlank() -> phone
            else -> ""
        }
    }
    
    /**
     * Get formatted credit limit
     */
    fun getFormattedCreditLimit(): String {
        return "MMK ${String.format("%,.0f", creditLimit)}"
    }
    
    /**
     * Check if customer is active
     */
    fun isActive(): Boolean {
        return active == 1
    }
    
    /**
     * Check if customer has location data
     */
    fun hasLocation(): Boolean {
        return latitude != null && longitude != null
    }
}

/**
 * API Response wrapper for customers
 */
data class CustomersResponse(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: List<RemoteCustomer>?,
    
    @SerializedName("total")
    val total: Int?
)

/**
 * API Request for customer search/filter
 */
data class CustomerRequest(
    val search: String? = null,
    val customerId: Int? = null,
    val customerCode: String? = null,
    val activeOnly: Boolean = true,
    val page: Int = 1,
    val limit: Int = 50
)
