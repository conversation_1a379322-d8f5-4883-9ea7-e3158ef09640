package com.intelligent.evolvaordercollection.data.repository

import com.intelligent.evolvaordercollection.data.model.RemoteProduct
import com.intelligent.evolvaordercollection.data.network.NetworkModule
import com.intelligent.evolvaordercollection.data.network.NetworkResult
import com.intelligent.evolvaordercollection.data.network.ProductApiService
import com.intelligent.evolvaordercollection.data.network.safeApiCall
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * Repository for handling remote product data from MySQL database
 */
class RemoteProductRepository {
    
    private val apiService: ProductApiService = NetworkModule.provideProductApiService()
    
    /**
     * Get all products with optional filters
     */
    fun getProducts(
        search: String? = null,
        categoryId: Int? = null,
        principalId: Int? = null,
        activeOnly: Boolean = true,
        page: Int = 1,
        limit: Int = 50
    ): Flow<NetworkResult<List<RemoteProduct>>> = flow {
        emit(NetworkResult.Loading())
        
        val result = safeApiCall {
            apiService.getProducts(search, categoryId, principalId, activeOnly, page, limit)
        }
        
        when (result) {
            is NetworkResult.Success -> {
                val products = result.data.data ?: emptyList()
                emit(NetworkResult.Success(products))
            }
            is NetworkResult.Error -> {
                emit(NetworkResult.Error(result.message, result.code))
            }
            is NetworkResult.Loading -> {
                // Already emitted loading state
            }
        }
    }
    
    /**
     * Search products by name or code
     */
    fun searchProducts(
        query: String,
        page: Int = 1,
        limit: Int = 50
    ): Flow<NetworkResult<List<RemoteProduct>>> = flow {
        emit(NetworkResult.Loading())
        
        val result = safeApiCall {
            apiService.searchProducts(query, page, limit)
        }
        
        when (result) {
            is NetworkResult.Success -> {
                val products = result.data.data ?: emptyList()
                emit(NetworkResult.Success(products))
            }
            is NetworkResult.Error -> {
                emit(NetworkResult.Error(result.message, result.code))
            }
            is NetworkResult.Loading -> {
                // Already emitted loading state
            }
        }
    }
    
    /**
     * Get products by category
     */
    fun getProductsByCategory(
        categoryId: Int,
        page: Int = 1,
        limit: Int = 50
    ): Flow<NetworkResult<List<RemoteProduct>>> = flow {
        emit(NetworkResult.Loading())
        
        val result = safeApiCall {
            apiService.getProductsByCategory(categoryId, page, limit)
        }
        
        when (result) {
            is NetworkResult.Success -> {
                val products = result.data.data ?: emptyList()
                emit(NetworkResult.Success(products))
            }
            is NetworkResult.Error -> {
                emit(NetworkResult.Error(result.message, result.code))
            }
            is NetworkResult.Loading -> {
                // Already emitted loading state
            }
        }
    }
    
    /**
     * Get products by principal
     */
    fun getProductsByPrincipal(
        principalId: Int,
        page: Int = 1,
        limit: Int = 50
    ): Flow<NetworkResult<List<RemoteProduct>>> = flow {
        emit(NetworkResult.Loading())
        
        val result = safeApiCall {
            apiService.getProductsByPrincipal(principalId, page, limit)
        }
        
        when (result) {
            is NetworkResult.Success -> {
                val products = result.data.data ?: emptyList()
                emit(NetworkResult.Success(products))
            }
            is NetworkResult.Error -> {
                emit(NetworkResult.Error(result.message, result.code))
            }
            is NetworkResult.Loading -> {
                // Already emitted loading state
            }
        }
    }
    
    /**
     * Get product by ID
     */
    suspend fun getProductById(productId: Int): NetworkResult<RemoteProduct?> {
        val result = safeApiCall {
            apiService.getProductById(productId)
        }
        
        return when (result) {
            is NetworkResult.Success -> {
                val product = result.data.data?.firstOrNull()
                NetworkResult.Success(product)
            }
            is NetworkResult.Error -> {
                NetworkResult.Error(result.message, result.code)
            }
            is NetworkResult.Loading -> {
                NetworkResult.Loading()
            }
        }
    }
    
    /**
     * Get product by product code
     */
    suspend fun getProductByCode(productCode: String): NetworkResult<RemoteProduct?> {
        val result = safeApiCall {
            apiService.getProductByCode(productCode)
        }
        
        return when (result) {
            is NetworkResult.Success -> {
                val product = result.data.data?.firstOrNull()
                NetworkResult.Success(product)
            }
            is NetworkResult.Error -> {
                NetworkResult.Error(result.message, result.code)
            }
            is NetworkResult.Loading -> {
                NetworkResult.Loading()
            }
        }
    }
}
