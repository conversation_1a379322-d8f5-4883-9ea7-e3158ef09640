package com.intelligent.evolvaordercollection.data.model

import com.google.gson.annotations.SerializedName

/**
 * User model for authentication
 */
data class User(
    @SerializedName("UserID")
    val userId: Int,

    @SerializedName("UserName")
    val userName: String,

    @SerializedName("SaleTeamID")
    val saleTeamId: Int,

    @SerializedName("SaleTeamName")
    val saleTeamName: String,

    @SerializedName("ActiveUser")
    val activeUser: Int
) {
    /**
     * Check if user is active
     */
    fun isActive(): Boolean = activeUser == 1
}

/**
 * Login request model
 */
data class LoginRequest(
    @SerializedName("username")
    val username: String,
    
    @SerializedName("password")
    val password: String
)

/**
 * Login response model
 */
data class LoginResponse(
    @SerializedName("success")
    val success: <PERSON><PERSON>an,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: LoginData?
)

/**
 * Login response data
 */
data class LoginData(
    @SerializedName("user")
    val user: User,
    
    @SerializedName("session_token")
    val sessionToken: String,
    
    @SerializedName("login_time")
    val loginTime: String
)

/**
 * User session for storing logged in user data
 */
data class UserSession(
    val user: User,
    val sessionToken: String,
    val loginTime: String,
    val isLoggedIn: Boolean = true
) {
    /**
     * Get default station number based on user
     */
    fun getStationNo(): String = "ST01" // Default station

    /**
     * Get sale team ID for orders
     */
    fun getSaleTeamId(): Int = user.saleTeamId
}
