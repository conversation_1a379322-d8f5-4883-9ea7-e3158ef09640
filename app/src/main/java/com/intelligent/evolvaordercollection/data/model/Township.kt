package com.intelligent.evolvaordercollection.data.model

import com.google.gson.annotations.SerializedName

/**
 * Township model for dropdown filters
 */
data class Township(
    @SerializedName("TownshipID")
    val townshipId: Int,

    @SerializedName("TownshipName")
    val townshipName: String,

    @SerializedName("CustomerCount")
    val customerCount: Int
)

/**
 * SaleTeam model for dropdown filters
 */
data class SaleTeam(
    @SerializedName("SaleTeamID")
    val saleTeamId: Int,

    @SerializedName("SaleTeam")
    val saleTeam: String,

    @SerializedName("SaleTeamDescription")
    val saleTeamDescription: String?,

    @SerializedName("CustomerCount")
    val customerCount: Int
)

/**
 * API Response wrappers
 */
data class TownshipsResponse(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: List<Township>?,
    
    @SerializedName("total")
    val total: Int?
)

data class SaleTeamsResponse(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: List<SaleTeam>?,
    
    @SerializedName("total")
    val total: Int?
)
