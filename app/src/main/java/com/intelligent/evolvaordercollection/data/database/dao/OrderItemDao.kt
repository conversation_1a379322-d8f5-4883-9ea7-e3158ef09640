package com.intelligent.evolvaordercollection.data.database.dao

import androidx.room.*
import com.intelligent.evolvaordercollection.data.model.OrderItem
import kotlinx.coroutines.flow.Flow

@Dao
interface OrderItemDao {
    
    @Query("SELECT * FROM order_items WHERE orderId = :orderId")
    fun getOrderItems(orderId: Long): Flow<List<OrderItem>>
    
    @Query("SELECT * FROM order_items WHERE id = :id")
    suspend fun getOrderItemById(id: Long): OrderItem?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrderItem(orderItem: OrderItem): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrderItems(orderItems: List<OrderItem>)
    
    @Update
    suspend fun updateOrderItem(orderItem: OrderItem)
    
    @Delete
    suspend fun deleteOrderItem(orderItem: OrderItem)
    
    @Query("DELETE FROM order_items WHERE orderId = :orderId")
    suspend fun deleteOrderItems(orderId: Long)
    
    @Query("UPDATE order_items SET quantity = :quantity, totalPrice = :totalPrice WHERE id = :id")
    suspend fun updateOrderItemQuantity(id: Long, quantity: Int, totalPrice: String)

    @Query("SELECT * FROM order_items WHERE orderId = :orderId")
    suspend fun getOrderItemsList(orderId: Long): List<OrderItem>
}
