package com.intelligent.evolvaordercollection.data.model

import android.content.Context
import android.os.Build
import java.math.BigDecimal
import java.util.UUID

// Extension functions to convert local models to cloud models
fun Product.toCloudProduct(deviceId: String): CloudProduct {
    return CloudProduct(
        id = this.id.toString(),
        name = this.name,
        description = this.description,
        price = this.price.toDouble(),
        category = this.category,
        productCode = this.productCode,
        barcode = this.barcode,
        stockQuantity = this.stockQuantity,
        imageUrl = this.imageUrl,
        isActive = this.isActive,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        deviceId = deviceId,
        lastSyncedAt = System.currentTimeMillis()
    )
}

fun Customer.toCloudCustomer(deviceId: String): CloudCustomer {
    return CloudCustomer(
        id = this.id.toString(),
        name = this.name,
        email = this.email,
        phone = this.phone,
        address = this.address,
        customerCode = this.customerCode,
        contactPerson = this.contactPerson,
        creditLimit = this.creditLimit.toDouble(),
        townshipId = this.townshipId,
        townshipName = this.townshipName,
        saleTeamId = this.saleTeamId,
        saleTeamName = this.saleTeamName,
        latitude = this.latitude,
        longitude = this.longitude,
        isActive = this.isActive,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        deviceId = deviceId,
        lastSyncedAt = System.currentTimeMillis()
    )
}

fun Order.toCloudOrder(deviceId: String, items: List<OrderItem> = emptyList()): CloudOrder {
    return CloudOrder(
        id = this.id.toString(),
        customerId = this.customerId?.toString() ?: "0",
        customerName = this.customerName,
        totalAmount = this.totalAmount.toDouble(),
        status = this.status.name,
        notes = this.notes,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        deviceId = deviceId,
        lastSyncedAt = System.currentTimeMillis(),
        items = items.map { it.toCloudOrderItem(deviceId) }
    )
}

fun OrderItem.toCloudOrderItem(deviceId: String): CloudOrderItem {
    return CloudOrderItem(
        id = this.id.toString(),
        orderId = this.orderId.toString(),
        productId = this.productId.toString(),
        productName = this.productName,
        quantity = this.quantity,
        unitPrice = this.unitPrice.toDouble(),
        totalPrice = this.totalPrice.toDouble(),
        discount = 0.0, // OrderItem doesn't have discount field, default to 0
        deviceId = deviceId,
        lastSyncedAt = System.currentTimeMillis()
    )
}

// Utility functions
fun generateDeviceId(context: Context): String {
    val sharedPrefs = context.getSharedPreferences("pos_device", Context.MODE_PRIVATE)
    var deviceId = sharedPrefs.getString("device_id", null)
    
    if (deviceId == null) {
        deviceId = "${Build.MODEL}_${UUID.randomUUID().toString().take(8)}"
        sharedPrefs.edit().putString("device_id", deviceId).apply()
    }
    
    return deviceId
}

fun getDeviceName(): String {
    return "${Build.MANUFACTURER} ${Build.MODEL}"
}

// Data validation extensions
fun Product.isValidForSync(): Boolean {
    return name.isNotBlank() && price > BigDecimal.ZERO
}

fun Customer.isValidForSync(): Boolean {
    return name.isNotBlank()
}

fun Order.isValidForSync(): Boolean {
    return (customerId ?: 0) >= 0 && totalAmount > BigDecimal.ZERO
}

// Conflict resolution helpers
fun Product.isNewerThan(other: Product): Boolean {
    return this.updatedAt > other.updatedAt
}

fun Customer.isNewerThan(other: Customer): Boolean {
    return this.updatedAt > other.updatedAt
}

fun Order.isNewerThan(other: Order): Boolean {
    return this.updatedAt > other.updatedAt
}

// Data integrity checks
fun validateProductIntegrity(products: List<Product>): List<String> {
    val errors = mutableListOf<String>()

    // Check for duplicate product codes
    val duplicateCodes = products.groupBy { it.productCode }
        .filter { it.key.isNotEmpty() && it.value.size > 1 }
        .keys

    if (duplicateCodes.isNotEmpty()) {
        errors.add("Duplicate product codes found: ${duplicateCodes.joinToString()}")
    }

    // Check for duplicate barcodes
    val duplicateBarcodes = products.groupBy { it.barcode }
        .filter { it.key.isNotEmpty() && it.value.size > 1 }
        .keys

    if (duplicateBarcodes.isNotEmpty()) {
        errors.add("Duplicate barcodes found: ${duplicateBarcodes.joinToString()}")
    }

    return errors
}

fun validateCustomerIntegrity(customers: List<Customer>): List<String> {
    val errors = mutableListOf<String>()

    // Check for duplicate emails
    val duplicateEmails = customers.groupBy { it.email }
        .filter { it.key.isNotEmpty() && it.value.size > 1 }
        .keys

    if (duplicateEmails.isNotEmpty()) {
        errors.add("Duplicate emails found: ${duplicateEmails.joinToString()}")
    }

    return errors
}

// Export helpers
fun productsToCsv(products: List<Product>): String {
    val header = "ID,Name,Description,Price,Category,Product Code,Barcode,Stock,Active,Created,Updated"
    val rows = products.map { product ->
        "${product.id},\"${product.name}\",\"${product.description}\",${product.price}," +
        "\"${product.category}\",\"${product.productCode}\",\"${product.barcode}\"," +
        "${product.stockQuantity},${product.isActive},${product.createdAt},${product.updatedAt}"
    }
    return listOf(header).plus(rows).joinToString("\n")
}

fun customersToCsv(customers: List<Customer>): String {
    val header = "ID,Name,Email,Phone,Address,Active,Created,Updated"
    val rows = customers.map { customer ->
        "${customer.id},\"${customer.name}\",\"${customer.email}\",\"${customer.phone}\"," +
        "\"${customer.address}\",${customer.isActive},${customer.createdAt},${customer.updatedAt}"
    }
    return listOf(header).plus(rows).joinToString("\n")
}

fun ordersToCsv(orders: List<Order>): String {
    val header = "ID,Customer ID,Customer Name,Total Amount,Status,Notes,Created,Updated"
    val rows = orders.map { order ->
        "${order.id},${order.customerId},\"${order.customerName}\",${order.totalAmount}," +
        "\"${order.status.name}\",\"${order.notes}\",${order.createdAt},${order.updatedAt}"
    }
    return listOf(header).plus(rows).joinToString("\n")
}
