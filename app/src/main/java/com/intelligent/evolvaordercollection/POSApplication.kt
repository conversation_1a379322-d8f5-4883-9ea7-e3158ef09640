package com.intelligent.evolvaordercollection

import android.app.Application
import com.intelligent.evolvaordercollection.di.AppContainer

class POSApplication : Application() {

    // Manual DI container
    lateinit var container: AppContainer
        private set

    override fun onCreate() {
        super.onCreate()
        container = AppContainer(this)

        // No sample data seeding - products will be loaded from remote database
    }
}
