{"formatVersion": 1, "database": {"version": 3, "identityHash": "5bb610b77d6553c32afcc983fbd68649", "entities": [{"tableName": "products", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `description` TEXT NOT NULL, `price` TEXT NOT NULL, `category` TEXT NOT NULL, `productCode` TEXT NOT NULL, `barcode` TEXT NOT NULL, `stockQuantity` INTEGER NOT NULL, `imageUrl` TEXT NOT NULL, `isActive` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "price", "columnName": "price", "affinity": "TEXT", "notNull": true}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productCode", "columnName": "productCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "barcode", "columnName": "barcode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "stockQuantity", "columnName": "stockQuantity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}, {"tableName": "customers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `email` TEXT NOT NULL, `phone` TEXT NOT NULL, `address` TEXT NOT NULL, `customerCode` TEXT NOT NULL, `contactPerson` TEXT NOT NULL, `creditLimit` TEXT NOT NULL, `isActive` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "address", "columnName": "address", "affinity": "TEXT", "notNull": true}, {"fieldPath": "customerCode", "columnName": "customerCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "creditLimit", "columnName": "creditLimit", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}, {"tableName": "orders", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `orderNumber` TEXT NOT NULL, `customerId` INTEGER, `customerName` TEXT NOT NULL, `status` TEXT NOT NULL, `subtotal` TEXT NOT NULL, `taxAmount` TEXT NOT NULL, `discountAmount` TEXT NOT NULL, `totalAmount` TEXT NOT NULL, `notes` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "orderNumber", "columnName": "orderNumber", "affinity": "TEXT", "notNull": true}, {"fieldPath": "customerId", "columnName": "customerId", "affinity": "INTEGER"}, {"fieldPath": "customerName", "columnName": "customerName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subtotal", "columnName": "subtotal", "affinity": "TEXT", "notNull": true}, {"fieldPath": "taxAmount", "columnName": "taxAmount", "affinity": "TEXT", "notNull": true}, {"fieldPath": "discountAmount", "columnName": "discountAmount", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalAmount", "columnName": "totalAmount", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}, {"tableName": "order_items", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `orderId` INTEGER NOT NULL, `productId` INTEGER NOT NULL, `productName` TEXT NOT NULL, `quantity` INTEGER NOT NULL, `unitPrice` TEXT NOT NULL, `totalPrice` TEXT NOT NULL, `notes` TEXT NOT NULL, FOREIGN KEY(`orderId`) REFERENCES `orders`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`productId`) REFERENCES `products`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "orderId", "columnName": "orderId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "productId", "columnName": "productId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "productName", "columnName": "productName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "quantity", "columnName": "quantity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unitPrice", "columnName": "unitPrice", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalPrice", "columnName": "totalPrice", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "foreignKeys": [{"table": "orders", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["orderId"], "referencedColumns": ["id"]}, {"table": "products", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["productId"], "referencedColumns": ["id"]}]}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '5bb610b77d6553c32afcc983fbd68649')"]}}