# App Signing Setup for Evolva Order Collection

## Overview
This project is now configured with proper app signing to reduce Google Play Protect warnings when installing APKs.

## Files Created
- `evolva-pos-keystore.jks` - The keystore file containing your signing certificate
- `keystore.properties` - Contains keystore credentials (excluded from git)
- `build-signed-apk.bat` - Convenient script to build signed APKs

## Keystore Details
- **Keystore File**: `evolva-pos-keystore.jks`
- **Key Alias**: `evolva-pos-key`
- **Validity**: 10,000 days (approximately 27 years)
- **Algorithm**: RSA 2048-bit

## Credentials
- **Store Password**: `evolvapos123`
- **Key Password**: `evolvapos123`

## Building Signed APKs

### Method 1: Using the Batch Script
```bash
# Double-click or run from command line
build-signed-apk.bat
```

### Method 2: Using Gradle Commands
```bash
# Debug APK (recommended for testing)
./gradlew assembleDebug

# Release APK (for production)
./gradlew assembleRelease
```

## APK Locations
- Debug APK: `app/build/outputs/apk/debug/app-debug.apk`
- Release APK: `app/build/outputs/apk/release/app-release.apk`

## Security Notes
1. The `keystore.properties` file is excluded from git for security
2. Keep the keystore file (`evolva-pos-keystore.jks`) secure and backed up
3. Never share the keystore passwords publicly
4. For production apps, consider using stronger passwords

## Reducing Google Play Protect Warnings
The signed APKs should show fewer warnings because:
1. They're signed with a consistent certificate
2. The certificate is valid for a long period
3. The signing configuration is properly set up

## For Production Distribution
Consider these additional steps:
1. Upload to Google Play Console for official distribution
2. Use Play Console's internal testing for team distribution
3. Consider Firebase App Distribution for beta testing
4. Generate an Android App Bundle (.aab) instead of APK for Play Store

## Troubleshooting
If you get signing errors:
1. Ensure `keystore.properties` exists in the root directory
2. Verify the keystore file path is correct
3. Check that the passwords match in `keystore.properties`
4. Make sure the keystore file hasn't been corrupted

## Backup Recommendation
**Important**: Back up your keystore file! If you lose it, you won't be able to update your app in the future.
