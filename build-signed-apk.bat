@echo off
echo Building signed APK for Evolva Order Collection...
echo.

echo Building debug APK with custom signing...
call gradlew assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Debug APK built successfully!
    echo Location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo This APK is signed with your custom keystore and should reduce Google Play Protect warnings.
) else (
    echo.
    echo ❌ Build failed. Please check the error messages above.
)

pause
