<?php
/**
 * Very simple test to check if the server and database are accessible
 * Upload this to: https://www.intelligentmyanmar.com/OrderCollectionApp(DemoROasis)/api/simple_test.php
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Database configuration
$host = '*************';
$dbname = 'DemoROasis';
$username = 'DemoROasisUser';
$password = '!nt3ll!g3nt';

$response = [
    'server_status' => 'running',
    'timestamp' => date('Y-m-d H:i:s'),
    'php_version' => phpversion(),
    'database_test' => 'not_tested'
];

try {
    // Test database connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $response['database_test'] = 'connected';
    
    // Test simple query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM Products");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $response['products_count'] = (int)$result['count'];
    $response['success'] = true;
    $response['message'] = 'Server and database are working correctly';
    
} catch (PDOException $e) {
    $response['database_test'] = 'failed';
    $response['database_error'] = $e->getMessage();
    $response['success'] = false;
    $response['message'] = 'Database connection failed';
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Server error: ' . $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
