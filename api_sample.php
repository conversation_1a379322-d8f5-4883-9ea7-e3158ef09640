<?php
/**
 * Sample API endpoint for OrderCollection App
 * This file should be placed on your server at:
 * https://www.intelligentmyanmar.com/OrderCollectionApp(DemoROasis)/api/products.php
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Database configuration
$host = '*************';
$dbname = 'DemoROasis';
$username = 'DemoROasisUser';
$password = '!nt3ll!g3nt';

try {
    // Create PDO connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get request parameters
    $search = $_GET['search'] ?? null;
    $categoryId = $_GET['category_id'] ?? null;
    $principalId = $_GET['principal_id'] ?? null;
    $activeOnly = $_GET['active_only'] ?? true;
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 50);
    $offset = ($page - 1) * $limit;
    
    // Build the SQL query
    $sql = "
        SELECT 
            p.ProductID,
            p.`Product Code`,
            p.ProductName,
            p.`Product Description`,
            p.CategoryID,
            c.CategoryName,
            p.PrincipalID,
            pr.PrincipalName,
            p.`Unit Size`,
            p.`Selling Price`,
            p.ProductImagePath,
            p.UnitsInStock,
            p.Discontinued
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Principals pr ON p.PrincipalID = pr.PrincipalID
        WHERE 1=1
    ";
    
    $params = [];
    
    // Add search filter
    if ($search) {
        $sql .= " AND (p.ProductName LIKE :search OR p.`Product Code` LIKE :search OR p.`Product Description` LIKE :search)";
        $params['search'] = "%$search%";
    }
    
    // Add category filter
    if ($categoryId) {
        $sql .= " AND p.CategoryID = :categoryId";
        $params['categoryId'] = $categoryId;
    }
    
    // Add principal filter
    if ($principalId) {
        $sql .= " AND p.PrincipalID = :principalId";
        $params['principalId'] = $principalId;
    }
    
    // Add active only filter
    if ($activeOnly) {
        $sql .= " AND p.Discontinued = 0";
    }
    
    // Add ordering and pagination
    $sql .= " ORDER BY p.ProductName ASC LIMIT :limit OFFSET :offset";
    
    // Prepare and execute the query
    $stmt = $pdo->prepare($sql);
    
    // Bind parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count for pagination
    $countSql = "
        SELECT COUNT(*) as total
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Principals pr ON p.PrincipalID = pr.PrincipalID
        WHERE 1=1
    ";
    
    if ($search) {
        $countSql .= " AND (p.ProductName LIKE :search OR p.`Product Code` LIKE :search OR p.`Product Description` LIKE :search)";
    }
    if ($categoryId) {
        $countSql .= " AND p.CategoryID = :categoryId";
    }
    if ($principalId) {
        $countSql .= " AND p.PrincipalID = :principalId";
    }
    if ($activeOnly) {
        $countSql .= " AND p.Discontinued = 0";
    }
    
    $countStmt = $pdo->prepare($countSql);
    foreach ($params as $key => $value) {
        if ($key !== 'limit' && $key !== 'offset') {
            $countStmt->bindValue(":$key", $value);
        }
    }
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Format the response
    $response = [
        'success' => true,
        'message' => 'Products retrieved successfully',
        'data' => $products,
        'total' => (int)$total,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => ceil($total / $limit)
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // Handle database errors
    $response = [
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Handle other errors
    $response = [
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'data' => null,
        'total' => 0
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
}
?>

<?php
/**
 * Additional API endpoints you can create:
 * 
 * 1. Get product by ID: products/{id}.php
 * 2. Get product by code: products/code/{code}.php
 * 3. Get categories: categories.php
 * 4. Get principals: principals.php
 * 5. Search products: products/search.php
 * 
 * Example usage:
 * GET /api/products.php - Get all products
 * GET /api/products.php?search=rice - Search for products containing "rice"
 * GET /api/products.php?category_id=1 - Get products in category 1
 * GET /api/products.php?principal_id=2 - Get products from principal 2
 * GET /api/products.php?page=2&limit=20 - Get page 2 with 20 items per page
 * GET /api/products.php?active_only=true - Get only active products
 */
?>
